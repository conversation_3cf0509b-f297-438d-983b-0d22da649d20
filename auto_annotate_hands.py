#!/usr/bin/env python3
"""
Auto-annotazione delle mani usando MediaPipe
Genera automaticamente file .txt in formato YOLO per le immagini di mani
"""

import cv2
import mediapipe as mp
import os
from pathlib import Path
import numpy as np

class HandAutoAnnotator:
    def __init__(self):
        # Inizializza MediaPipe Hands
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=True,
            max_num_hands=2,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
    def detect_hands_in_image(self, image_path):
        """Rileva mani in un'immagine e restituisce bounding boxes"""
        try:
            # Leggi immagine
            image = cv2.imread(str(image_path))
            if image is None:
                return []
            
            # Converti BGR to RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Rileva mani
            results = self.hands.process(rgb_image)
            
            bboxes = []
            if results.multi_hand_landmarks:
                h, w, _ = image.shape
                
                for hand_landmarks in results.multi_hand_landmarks:
                    # Estrai coordinate x,y di tutti i landmark
                    x_coords = [landmark.x * w for landmark in hand_landmarks.landmark]
                    y_coords = [landmark.y * h for landmark in hand_landmarks.landmark]
                    
                    # Calcola bounding box
                    x_min = max(0, min(x_coords) - 20)  # Padding
                    x_max = min(w, max(x_coords) + 20)
                    y_min = max(0, min(y_coords) - 20)
                    y_max = min(h, max(y_coords) + 20)
                    
                    # Converti in formato YOLO (normalizzato)
                    x_center = ((x_min + x_max) / 2) / w
                    y_center = ((y_min + y_max) / 2) / h
                    width = (x_max - x_min) / w
                    height = (y_max - y_min) / h
                    
                    bboxes.append((0, x_center, y_center, width, height))
            
            return bboxes
            
        except Exception as e:
            print(f"Errore processing {image_path}: {e}")
            return []
    
    def annotate_dataset(self, images_dir, labels_dir, max_images=None):
        """Annota automaticamente tutte le immagini nel dataset"""
        images_dir = Path(images_dir)
        labels_dir = Path(labels_dir)
        
        # Crea directory labels se non esiste
        labels_dir.mkdir(parents=True, exist_ok=True)
        
        # Trova tutte le immagini
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        for ext in image_extensions:
            image_files.extend(images_dir.glob(f"*{ext}"))
            image_files.extend(images_dir.glob(f"*{ext.upper()}"))
        
        if max_images:
            image_files = image_files[:max_images]
        
        print(f"🔍 Trovate {len(image_files)} immagini da annotare")
        
        annotated_count = 0
        hands_detected_count = 0
        
        for i, image_path in enumerate(image_files):
            if i % 100 == 0:
                print(f"📊 Progresso: {i}/{len(image_files)} ({i/len(image_files)*100:.1f}%)")
            
            # Rileva mani
            bboxes = self.detect_hands_in_image(image_path)
            
            # Crea file label
            label_path = labels_dir / f"{image_path.stem}.txt"
            
            with open(label_path, 'w') as f:
                for bbox in bboxes:
                    class_id, x_center, y_center, width, height = bbox
                    f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
            
            annotated_count += 1
            if bboxes:
                hands_detected_count += 1
        
        print(f"\\n✅ Annotazione completata!")
        print(f"📊 Statistiche:")
        print(f"   - Immagini processate: {annotated_count}")
        print(f"   - Immagini con mani rilevate: {hands_detected_count}")
        print(f"   - Tasso di rilevamento: {hands_detected_count/annotated_count*100:.1f}%")
        
        return annotated_count, hands_detected_count
    
    def validate_annotations(self, images_dir, labels_dir, sample_size=10):
        """Valida un campione di annotazioni"""
        images_dir = Path(images_dir)
        labels_dir = Path(labels_dir)
        
        # Prendi campione casuale
        image_files = list(images_dir.glob("*.jpg"))[:sample_size]
        
        print(f"🔍 Validazione su {len(image_files)} immagini campione...")
        
        for image_path in image_files:
            label_path = labels_dir / f"{image_path.stem}.txt"
            
            if not label_path.exists():
                print(f"❌ Missing label: {label_path}")
                continue
            
            # Leggi immagine
            image = cv2.imread(str(image_path))
            h, w, _ = image.shape
            
            # Leggi annotazioni
            with open(label_path, 'r') as f:
                lines = f.readlines()
            
            # Disegna bounding boxes
            for line in lines:
                parts = line.strip().split()
                if len(parts) == 5:
                    class_id, x_center, y_center, width, height = map(float, parts)
                    
                    # Converti da normalizzato a pixel
                    x1 = int((x_center - width/2) * w)
                    y1 = int((y_center - height/2) * h)
                    x2 = int((x_center + width/2) * w)
                    y2 = int((y_center + height/2) * h)
                    
                    # Disegna bbox
                    cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(image, 'hand', (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
            
            # Salva immagine con annotazioni per verifica
            output_path = f"validation_{image_path.name}"
            cv2.imwrite(output_path, image)
            print(f"✅ Validazione salvata: {output_path}")
        
        print("✅ Validazione completata!")

def main():
    print("🖐️ Hand Auto-Annotator con MediaPipe")
    print("=" * 50)
    
    annotator = HandAutoAnnotator()
    
    # Percorsi
    train_images = Path("datasets/hands/images/train")
    train_labels = Path("datasets/hands/labels/train")
    
    print("Opzioni disponibili:")
    print("1. 🚀 Annota tutte le immagini (può richiedere ore)")
    print("2. 🧪 Test su 100 immagini")
    print("3. ✅ Valida annotazioni esistenti")
    print("4. 📊 Conta immagini e labels")
    
    choice = input("\\nScelta (1-4): ").strip()
    
    if choice == "1":
        print("⚠️  ATTENZIONE: Questo processo può richiedere diverse ore!")
        confirm = input("Continuare? (y/n): ").lower()
        if confirm == 'y':
            annotator.annotate_dataset(train_images, train_labels)
    
    elif choice == "2":
        print("🧪 Test su 100 immagini...")
        annotator.annotate_dataset(train_images, train_labels, max_images=100)
    
    elif choice == "3":
        annotator.validate_annotations(train_images, train_labels)
    
    elif choice == "4":
        # Conta file
        images = list(train_images.glob("*.jpg"))
        labels = list(train_labels.glob("*.txt"))
        
        print(f"📊 Statistiche dataset:")
        print(f"   - Immagini: {len(images)}")
        print(f"   - Labels: {len(labels)}")
        print(f"   - Mancanti: {len(images) - len(labels)}")
        
        if len(images) > len(labels):
            print(f"\\n💡 Suggerimento: Esegui auto-annotazione per le {len(images) - len(labels)} immagini mancanti")
    
    else:
        print("❌ Scelta non valida")

if __name__ == "__main__":
    main()
