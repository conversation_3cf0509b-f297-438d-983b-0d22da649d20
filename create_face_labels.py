#!/usr/bin/env python3
"""
Crea automaticamente file .txt con etichette per face detection
Usa OpenCV per rilevare visi e convertire in formato YOLO
"""

import cv2
import os
from pathlib import Path
import glob

def detect_faces_opencv(image_path):
    """Rileva visi usando OpenCV Haar Cascade"""
    
    # Carica il classificatore Haar per visi
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    # Leggi immagine
    img = cv2.imread(image_path)
    if img is None:
        print(f"❌ Impossibile leggere: {image_path}")
        return []
    
    # Converti in scala di grigi
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Rileva visi
    faces = face_cascade.detectMultiScale(
        gray,
        scaleFactor=1.1,
        minNeighbors=5,
        minSize=(30, 30),
        flags=cv2.CASCADE_SCALE_IMAGE
    )
    
    # Converti coordinate in formato YOLO (normalizzate)
    h, w = img.shape[:2]
    yolo_boxes = []
    
    for (x, y, w_box, h_box) in faces:
        # Calcola centro e dimensioni normalizzate
        center_x = (x + w_box/2) / w
        center_y = (y + h_box/2) / h
        width = w_box / w
        height = h_box / h
        
        # Formato YOLO: class_id center_x center_y width height
        yolo_boxes.append(f"0 {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")
    
    return yolo_boxes

def create_labels_for_directory(images_dir, labels_dir):
    """Crea etichette per tutte le immagini in una directory"""
    
    # Crea directory labels se non esiste
    os.makedirs(labels_dir, exist_ok=True)
    
    # Estensioni supportate
    extensions = ['*.jpg', '*.jpeg', '*.png', '*.JPG', '*.JPEG', '*.PNG']
    
    # Trova tutte le immagini
    image_files = []
    for ext in extensions:
        image_files.extend(glob.glob(os.path.join(images_dir, ext)))
    
    print(f"🔍 Trovate {len(image_files)} immagini in {images_dir}")
    
    created_labels = 0
    total_faces = 0
    
    for image_path in image_files:
        # Nome file senza estensione
        base_name = Path(image_path).stem
        label_path = os.path.join(labels_dir, f"{base_name}.txt")
        
        # Salta se etichetta già esistente
        if os.path.exists(label_path):
            print(f"⏭️  Saltato {base_name} (etichetta già esistente)")
            continue
        
        # Rileva visi
        faces = detect_faces_opencv(image_path)
        
        if faces:
            # Salva etichette
            with open(label_path, 'w') as f:
                for face in faces:
                    f.write(face + '\n')
            
            created_labels += 1
            total_faces += len(faces)
            print(f"✅ {base_name}: {len(faces)} visi rilevati")
        else:
            # Crea file vuoto se nessun viso rilevato
            with open(label_path, 'w') as f:
                pass
            print(f"⚠️  {base_name}: nessun viso rilevato")
            created_labels += 1
    
    print(f"\n📊 RISULTATI:")
    print(f"   📁 Etichette create: {created_labels}")
    print(f"   👤 Visi totali rilevati: {total_faces}")
    print(f"   📈 Media visi per immagine: {total_faces/max(created_labels,1):.1f}")

def main():
    """Funzione principale"""
    
    print("🎯 CREAZIONE AUTOMATICA ETICHETTE FACE DETECTION")
    print("=" * 50)
    
    # Directory
    train_images = "datasets/face_small/images/train"
    train_labels = "datasets/face_small/labels/train"
    
    # Verifica che la directory immagini esista
    if not os.path.exists(train_images):
        print(f"❌ Directory non trovata: {train_images}")
        return
    
    # Crea etichette per training
    print(f"\n🔄 Processando directory training...")
    create_labels_for_directory(train_images, train_labels)
    
    print(f"\n🎉 COMPLETATO! Etichette salvate in: {train_labels}")
    print(f"💡 Ora puoi riavviare il training con il dataset ampliato!")

if __name__ == "__main__":
    main()
