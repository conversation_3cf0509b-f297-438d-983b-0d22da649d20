#!/usr/bin/env python3
"""
Test Riconoscimento Emozioni Facciali
Usa MediaPipe Face Mesh per analizzare espressioni
"""

import cv2
import mediapipe as mp
import numpy as np
import time
from collections import deque

class EmotionRecognizer:
    def __init__(self):
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Storico emozioni per smoothing
        self.emotion_history = deque(maxlen=10)
        self.current_emotion = "Neutral"
        self.emotion_confidence = 0.0
        
        # Contatori emozioni
        self.emotion_counts = {
            "Happy": 0,
            "Sad": 0,
            "Surprised": 0,
            "Angry": 0,
            "Sleepy": 0,
            "Neutral": 0
        }
        
        print("✅ Emotion Recognizer inizializzato")
    
    def calculate_distance(self, point1, point2):
        """Calcola distanza euclidea tra due punti"""
        return np.sqrt((point1.x - point2.x)**2 + (point1.y - point2.y)**2)
    
    def analyze_facial_features(self, landmarks):
        """Analizza caratteristiche facciali per determinare emozione"""
        try:
            # Punti chiave del viso (MediaPipe Face Mesh indices)
            # Bocca
            mouth_left = landmarks.landmark[61]    # Angolo sinistro bocca
            mouth_right = landmarks.landmark[291]  # Angolo destro bocca
            mouth_top = landmarks.landmark[13]     # Labbro superiore centro
            mouth_bottom = landmarks.landmark[14]  # Labbro inferiore centro
            mouth_center = landmarks.landmark[17]  # Centro bocca
            
            # Occhi
            left_eye_top = landmarks.landmark[159]     # Palpebra superiore sx
            left_eye_bottom = landmarks.landmark[145]  # Palpebra inferiore sx
            right_eye_top = landmarks.landmark[386]    # Palpebra superiore dx
            right_eye_bottom = landmarks.landmark[374] # Palpebra inferiore dx
            
            # Sopracciglia
            left_eyebrow = landmarks.landmark[70]   # Sopracciglio sinistro
            right_eyebrow = landmarks.landmark[300] # Sopracciglio destro
            
            # Calcola metriche
            # 1. Sorriso (larghezza vs altezza bocca)
            mouth_width = self.calculate_distance(mouth_left, mouth_right)
            mouth_height = self.calculate_distance(mouth_top, mouth_bottom)
            smile_ratio = mouth_width / (mouth_height + 0.001)  # Evita divisione per zero
            
            # 2. Apertura occhi
            left_eye_openness = self.calculate_distance(left_eye_top, left_eye_bottom)
            right_eye_openness = self.calculate_distance(right_eye_top, right_eye_bottom)
            avg_eye_openness = (left_eye_openness + right_eye_openness) / 2
            
            # 3. Posizione sopracciglia (per sorpresa/rabbia)
            eyebrow_height = (left_eyebrow.y + right_eyebrow.y) / 2
            
            # 4. Curvatura bocca (su/giù)
            mouth_curve = mouth_center.y - (mouth_top.y + mouth_bottom.y) / 2
            
            # Logica di classificazione emozioni
            emotion = "Neutral"
            confidence = 0.5
            
            # Happy: sorriso largo, bocca larga
            if smile_ratio > 8.0 and mouth_curve < -0.002:
                emotion = "Happy"
                confidence = min(smile_ratio / 10.0, 1.0)
            
            # Surprised: occhi molto aperti, sopracciglia alzate, bocca aperta
            elif avg_eye_openness > 0.015 and eyebrow_height < 0.35 and mouth_height > 0.015:
                emotion = "Surprised"
                confidence = min(avg_eye_openness * 50, 1.0)
            
            # Sleepy: occhi chiusi o semi-chiusi
            elif avg_eye_openness < 0.008:
                emotion = "Sleepy"
                confidence = 1.0 - (avg_eye_openness * 100)
            
            # Sad: bocca verso il basso, occhi leggermente chiusi
            elif mouth_curve > 0.002 and smile_ratio < 6.0:
                emotion = "Sad"
                confidence = mouth_curve * 200
            
            # Angry: sopracciglia abbassate, bocca tesa
            elif eyebrow_height > 0.38 and smile_ratio < 5.0:
                emotion = "Angry"
                confidence = (eyebrow_height - 0.38) * 10
            
            # Limita confidence
            confidence = max(0.0, min(1.0, confidence))
            
            return emotion, confidence, {
                'smile_ratio': smile_ratio,
                'eye_openness': avg_eye_openness,
                'eyebrow_height': eyebrow_height,
                'mouth_curve': mouth_curve,
                'mouth_width': mouth_width,
                'mouth_height': mouth_height
            }
            
        except Exception as e:
            return "Error", 0.0, {}
    
    def smooth_emotion(self, emotion, confidence):
        """Applica smoothing alle emozioni per ridurre flickering"""
        self.emotion_history.append((emotion, confidence))
        
        if len(self.emotion_history) < 3:
            return emotion, confidence
        
        # Conta occorrenze recenti
        recent_emotions = {}
        total_confidence = 0
        
        for emo, conf in self.emotion_history:
            if emo not in recent_emotions:
                recent_emotions[emo] = []
            recent_emotions[emo].append(conf)
            total_confidence += conf
        
        # Trova emozione più frequente con confidence alta
        best_emotion = emotion
        best_score = 0
        
        for emo, confidences in recent_emotions.items():
            avg_conf = sum(confidences) / len(confidences)
            score = len(confidences) * avg_conf
            
            if score > best_score:
                best_emotion = emo
                best_score = score
        
        return best_emotion, total_confidence / len(self.emotion_history)
    
    def process_frame(self, frame):
        """Processa frame per riconoscimento emozioni"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(rgb_frame)
        
        face_data = None
        
        if results.multi_face_landmarks:
            for face_landmarks in results.multi_face_landmarks:
                # Analizza emozione
                emotion, confidence, metrics = self.analyze_facial_features(face_landmarks)
                
                # Applica smoothing
                smooth_emotion, smooth_confidence = self.smooth_emotion(emotion, confidence)
                
                # Aggiorna stato
                self.current_emotion = smooth_emotion
                self.emotion_confidence = smooth_confidence
                self.emotion_counts[smooth_emotion] += 1
                
                face_data = {
                    'landmarks': face_landmarks,
                    'emotion': smooth_emotion,
                    'confidence': smooth_confidence,
                    'raw_emotion': emotion,
                    'raw_confidence': confidence,
                    'metrics': metrics
                }
                break  # Solo un viso
        
        return face_data
    
    def draw_emotion_analysis(self, frame, face_data):
        """Disegna analisi emozioni"""
        if face_data:
            landmarks = face_data['landmarks']
            emotion = face_data['emotion']
            confidence = face_data['confidence']
            metrics = face_data['metrics']
            
            # Disegna face mesh (semplificato)
            self.mp_drawing.draw_landmarks(
                frame, landmarks, self.mp_face_mesh.FACEMESH_CONTOURS,
                None, 
                self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=1, circle_radius=1)
            )
            
            # Evidenzia punti chiave
            h, w, _ = frame.shape
            
            # Occhi
            left_eye = landmarks.landmark[159]
            right_eye = landmarks.landmark[386]
            cv2.circle(frame, (int(left_eye.x * w), int(left_eye.y * h)), 3, (255, 0, 0), -1)
            cv2.circle(frame, (int(right_eye.x * w), int(right_eye.y * h)), 3, (255, 0, 0), -1)
            
            # Bocca
            mouth_left = landmarks.landmark[61]
            mouth_right = landmarks.landmark[291]
            mouth_top = landmarks.landmark[13]
            mouth_bottom = landmarks.landmark[14]
            
            mouth_points = [
                (int(mouth_left.x * w), int(mouth_left.y * h)),
                (int(mouth_right.x * w), int(mouth_right.y * h)),
                (int(mouth_top.x * w), int(mouth_top.y * h)),
                (int(mouth_bottom.x * w), int(mouth_bottom.y * h))
            ]
            
            for point in mouth_points:
                cv2.circle(frame, point, 3, (0, 0, 255), -1)
            
            # Testo emozione principale
            emotion_color = {
                "Happy": (0, 255, 0),
                "Sad": (255, 0, 0),
                "Surprised": (0, 255, 255),
                "Angry": (0, 0, 255),
                "Sleepy": (128, 0, 128),
                "Neutral": (128, 128, 128)
            }.get(emotion, (255, 255, 255))
            
            # Emozione grande
            cv2.putText(frame, f"{emotion}", (50, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2.0, emotion_color, 3)
            
            # Confidence
            cv2.putText(frame, f"Confidence: {confidence:.2f}", (50, 140), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, emotion_color, 2)
            
            # Metriche dettagliate
            if metrics:
                metric_text = [
                    f"Smile Ratio: {metrics.get('smile_ratio', 0):.2f}",
                    f"Eye Open: {metrics.get('eye_openness', 0):.3f}",
                    f"Eyebrow: {metrics.get('eyebrow_height', 0):.3f}",
                    f"Mouth Curve: {metrics.get('mouth_curve', 0):.3f}"
                ]
                
                for i, text in enumerate(metric_text):
                    cv2.putText(frame, text, (frame.shape[1] - 300, 50 + i * 25), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return frame

def main():
    print("😊 TEST RICONOSCIMENTO EMOZIONI")
    print("=" * 40)
    print("🎭 Emozioni supportate:")
    print("   😊 Happy - Sorriso")
    print("   😢 Sad - Tristezza")
    print("   😲 Surprised - Sorpresa")
    print("   😠 Angry - Rabbia")
    print("   😴 Sleepy - Sonnolenza")
    print("   😐 Neutral - Neutrale")
    print("=" * 40)
    
    # Inizializza recognizer
    recognizer = EmotionRecognizer()
    
    # Inizializza webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Impossibile aprire la webcam")
        return
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("✅ Webcam inizializzata")
    print("📋 Comandi: 'q' per uscire, 's' per screenshot, 'r' per reset")
    
    # Statistiche
    fps_counter = 0
    fps_start_time = time.time()
    current_fps = 0
    screenshot_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip per effetto specchio
            frame = cv2.flip(frame, 1)
            
            # Processa emozioni
            start_time = time.time()
            face_data = recognizer.process_frame(frame)
            inference_time = (time.time() - start_time) * 1000
            
            # Disegna analisi
            frame = recognizer.draw_emotion_analysis(frame, face_data)
            
            # Calcola FPS
            fps_counter += 1
            if time.time() - fps_start_time >= 1.0:
                current_fps = fps_counter
                fps_counter = 0
                fps_start_time = time.time()
            
            # Informazioni
            info_text = [
                f"FPS: {current_fps}",
                f"Inference: {inference_time:.1f}ms",
                f"Viso rilevato: {'SI' if face_data else 'NO'}"
            ]
            
            # Disegna info
            y_offset = 200
            for i, text in enumerate(info_text):
                cv2.putText(frame, text, (10, y_offset + i * 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # Statistiche emozioni
            y_offset = 300
            cv2.putText(frame, "Statistiche Emozioni:", (10, y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            for i, (emotion, count) in enumerate(recognizer.emotion_counts.items()):
                if count > 0:
                    cv2.putText(frame, f"{emotion}: {count}", (10, y_offset + 25 + i * 20), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Istruzioni
            cv2.putText(frame, "q:quit  s:save  r:reset stats", 
                       (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Mostra frame
            cv2.imshow('Emotion Recognition Test', frame)
            
            # Input
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q') or key == 27:
                break
            elif key == ord('s'):
                screenshot_count += 1
                filename = f"emotion_test_{screenshot_count}.jpg"
                cv2.imwrite(filename, frame)
                print(f"📸 Screenshot salvato: {filename}")
            elif key == ord('r'):
                recognizer.emotion_counts = {k: 0 for k in recognizer.emotion_counts}
                recognizer.emotion_history.clear()
                print("🔄 Statistiche resettate")
    
    except KeyboardInterrupt:
        print("\n⏹️ Interruzione da tastiera")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n📊 STATISTICHE FINALI EMOZIONI:")
        total_detections = sum(recognizer.emotion_counts.values())
        for emotion, count in sorted(recognizer.emotion_counts.items(), key=lambda x: x[1], reverse=True):
            if count > 0:
                percentage = (count / total_detections) * 100 if total_detections > 0 else 0
                print(f"   {emotion}: {count} ({percentage:.1f}%)")
        
        print(f"\n📸 Screenshot salvati: {screenshot_count}")
        print("✅ Test emozioni completato")

if __name__ == "__main__":
    main()
