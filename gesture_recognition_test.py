#!/usr/bin/env python3
"""
Test Riconoscimento Gesti delle Mani
Versione semplificata per testare la funzionalità
"""

import cv2
import mediapipe as mp
import math
import time

class GestureRecognizer:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        print("✅ Gesture Recognizer inizializzato")
    
    def calculate_distance(self, point1, point2):
        """Calcola distanza tra due punti"""
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def recognize_gesture(self, landmarks, hand_label):
        """Riconosce il gesto della mano"""
        # Punti chiave della mano
        thumb_tip = landmarks.landmark[4]
        thumb_ip = landmarks.landmark[3]
        index_tip = landmarks.landmark[8]
        index_pip = landmarks.landmark[6]
        middle_tip = landmarks.landmark[12]
        middle_pip = landmarks.landmark[10]
        ring_tip = landmarks.landmark[16]
        ring_pip = landmarks.landmark[14]
        pinky_tip = landmarks.landmark[20]
        pinky_pip = landmarks.landmark[18]
        
        # Rileva dita estese
        fingers_up = []
        
        # Pollice (diverso per mano destra/sinistra)
        if hand_label == "Right":
            fingers_up.append(thumb_tip.x > thumb_ip.x)
        else:
            fingers_up.append(thumb_tip.x < thumb_ip.x)
        
        # Altre dita (estese se tip è sopra pip)
        fingers_up.append(index_tip.y < index_pip.y)
        fingers_up.append(middle_tip.y < middle_pip.y)
        fingers_up.append(ring_tip.y < ring_pip.y)
        fingers_up.append(pinky_tip.y < pinky_pip.y)
        
        total_fingers = sum(fingers_up)
        
        # Riconoscimento gesti
        if total_fingers == 0:
            return "✊ Pugno"
        elif total_fingers == 1 and fingers_up[1]:
            return "👆 Indice"
        elif total_fingers == 2 and fingers_up[1] and fingers_up[2]:
            return "✌️ Pace"
        elif total_fingers == 3 and fingers_up[1] and fingers_up[2] and fingers_up[3]:
            return "🤟 Tre"
        elif total_fingers == 4 and not fingers_up[0]:
            return "🖐️ Quattro"
        elif total_fingers == 5:
            return "✋ Mano Aperta"
        elif fingers_up[0] and fingers_up[1] and not any(fingers_up[2:]):
            return "🔫 Pistola"
        elif fingers_up[0] and not any(fingers_up[1:]):
            return "👍 Pollice Su"
        
        # Gesti speciali basati su distanze
        h, w = 480, 640
        thumb_tip_px = (int(thumb_tip.x * w), int(thumb_tip.y * h))
        index_tip_px = (int(index_tip.x * w), int(index_tip.y * h))
        
        thumb_index_dist = self.calculate_distance(thumb_tip_px, index_tip_px)
        if thumb_index_dist < 40:
            return "🤏 Pinch"
        
        return "❓ Sconosciuto"
    
    def process_frame(self, frame):
        """Processa il frame e rileva gesti"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_frame)
        
        gestures = []
        
        if results.multi_hand_landmarks:
            for hand_idx, hand_landmarks in enumerate(results.multi_hand_landmarks):
                # Info mano
                hand_label = results.multi_handedness[hand_idx].classification[0].label
                confidence = results.multi_handedness[hand_idx].classification[0].score
                
                # Riconosci gesto
                gesture = self.recognize_gesture(hand_landmarks, hand_label)
                
                # Calcola bounding box
                h, w, _ = frame.shape
                x_coords = [landmark.x * w for landmark in hand_landmarks.landmark]
                y_coords = [landmark.y * h for landmark in hand_landmarks.landmark]
                
                x_min, x_max = int(min(x_coords)), int(max(x_coords))
                y_min, y_max = int(min(y_coords)), int(max(y_coords))
                
                gestures.append({
                    'landmarks': hand_landmarks,
                    'label': hand_label,
                    'gesture': gesture,
                    'confidence': confidence,
                    'bbox': (x_min, y_min, x_max, y_max)
                })
        
        return gestures
    
    def draw_gestures(self, frame, gestures):
        """Disegna i gesti rilevati"""
        for gesture_data in gestures:
            landmarks = gesture_data['landmarks']
            label = gesture_data['label']
            gesture = gesture_data['gesture']
            confidence = gesture_data['confidence']
            bbox = gesture_data['bbox']
            
            x_min, y_min, x_max, y_max = bbox
            
            # Colore basato sulla mano
            color = (255, 0, 0) if label == "Right" else (0, 0, 255)
            
            # Disegna bounding box
            cv2.rectangle(frame, (x_min - 20, y_min - 20), (x_max + 20, y_max + 20), color, 2)
            
            # Disegna landmarks
            self.mp_drawing.draw_landmarks(
                frame, landmarks, self.mp_hands.HAND_CONNECTIONS,
                self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                self.mp_drawing.DrawingSpec(color=(255, 255, 255), thickness=2)
            )
            
            # Testo gesto
            gesture_text = f"{label}: {gesture}"
            cv2.putText(frame, gesture_text, (x_min - 20, y_min - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
            
            # Confidence
            conf_text = f"Conf: {confidence:.2f}"
            cv2.putText(frame, conf_text, (x_min - 20, y_min - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return frame

def main():
    print("🤏 TEST RICONOSCIMENTO GESTI")
    print("=" * 40)
    print("🎯 Gesti supportati:")
    print("   ✊ Pugno (0 dita)")
    print("   👆 Indice (1 dito)")
    print("   ✌️ Pace (2 dita)")
    print("   🤟 Tre (3 dita)")
    print("   🖐️ Quattro (4 dita)")
    print("   ✋ Mano Aperta (5 dita)")
    print("   👍 Pollice Su")
    print("   🔫 Pistola")
    print("   🤏 Pinch")
    print("=" * 40)
    
    # Inizializza recognizer
    recognizer = GestureRecognizer()
    
    # Inizializza webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Impossibile aprire la webcam")
        return
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("✅ Webcam inizializzata")
    print("📋 Comandi: 'q' per uscire, 's' per screenshot")
    
    # Statistiche
    fps_counter = 0
    fps_start_time = time.time()
    current_fps = 0
    gesture_counts = {}
    screenshot_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip per effetto specchio
            frame = cv2.flip(frame, 1)
            
            # Processa gesti
            start_time = time.time()
            gestures = recognizer.process_frame(frame)
            inference_time = (time.time() - start_time) * 1000
            
            # Disegna gesti
            frame = recognizer.draw_gestures(frame, gestures)
            
            # Conta gesti
            for gesture_data in gestures:
                gesture = gesture_data['gesture']
                gesture_counts[gesture] = gesture_counts.get(gesture, 0) + 1
            
            # Calcola FPS
            fps_counter += 1
            if time.time() - fps_start_time >= 1.0:
                current_fps = fps_counter
                fps_counter = 0
                fps_start_time = time.time()
            
            # Informazioni
            info_text = [
                f"FPS: {current_fps}",
                f"Inference: {inference_time:.1f}ms",
                f"Mani rilevate: {len(gestures)}",
                f"Gesti unici: {len(gesture_counts)}"
            ]
            
            # Disegna info
            y_offset = 30
            for i, text in enumerate(info_text):
                cv2.putText(frame, text, (10, y_offset + i * 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # Mostra gesti più frequenti
            if gesture_counts:
                most_common = max(gesture_counts, key=gesture_counts.get)
                cv2.putText(frame, f"Gesto più frequente: {most_common}", 
                           (10, frame.shape[0] - 40), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
            
            # Istruzioni
            cv2.putText(frame, "Press 'q' to quit, 's' to save screenshot", 
                       (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Mostra frame
            cv2.imshow('Gesture Recognition Test', frame)
            
            # Input
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q') or key == 27:
                break
            elif key == ord('s'):
                screenshot_count += 1
                filename = f"gesture_test_{screenshot_count}.jpg"
                cv2.imwrite(filename, frame)
                print(f"📸 Screenshot salvato: {filename}")
    
    except KeyboardInterrupt:
        print("\n⏹️ Interruzione da tastiera")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n📊 STATISTICHE GESTI:")
        for gesture, count in sorted(gesture_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   {gesture}: {count} volte")
        print(f"\n📸 Screenshot salvati: {screenshot_count}")
        print("✅ Test completato")

if __name__ == "__main__":
    main()
