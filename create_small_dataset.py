#!/usr/bin/env python3
"""
Crea un dataset ridotto con solo 100 immagini per training veloce
"""

import os
import shutil
from pathlib import Path

def create_small_dataset():
    """Crea dataset ridotto con 100 immagini train + 20 val"""
    
    # Percorsi
    original_train_images = "datasets/face/images/train"
    original_train_labels = "datasets/face/labels/train"
    original_val_images = "datasets/face/images/val"
    original_val_labels = "datasets/face/labels/val"
    
    # Nuovo dataset ridotto
    small_train_images = "datasets/face_small/images/train"
    small_train_labels = "datasets/face_small/labels/train"
    small_val_images = "datasets/face_small/images/val"
    small_val_labels = "datasets/face_small/labels/val"
    
    # Crea cartelle
    for path in [small_train_images, small_train_labels, small_val_images, small_val_labels]:
        os.makedirs(path, exist_ok=True)
    
    print("🎯 Creazione dataset ridotto per training veloce...")
    
    # Copia prime 100 immagini di training
    train_images = sorted(os.listdir(original_train_images))[:100]
    print(f"📊 Copiando {len(train_images)} immagini di training...")
    
    for img_name in train_images:
        # Copia immagine
        src_img = os.path.join(original_train_images, img_name)
        dst_img = os.path.join(small_train_images, img_name)
        shutil.copy2(src_img, dst_img)
        
        # Copia label corrispondente
        label_name = img_name.replace('.jpg', '.txt')
        src_label = os.path.join(original_train_labels, label_name)
        dst_label = os.path.join(small_train_labels, label_name)
        
        if os.path.exists(src_label):
            shutil.copy2(src_label, dst_label)
    
    # Copia prime 20 immagini di validazione
    val_images = sorted(os.listdir(original_val_images))[:20]
    print(f"📊 Copiando {len(val_images)} immagini di validazione...")
    
    for img_name in val_images:
        # Copia immagine
        src_img = os.path.join(original_val_images, img_name)
        dst_img = os.path.join(small_val_images, img_name)
        shutil.copy2(src_img, dst_img)
        
        # Copia label corrispondente
        label_name = img_name.replace('.jpg', '.txt')
        src_label = os.path.join(original_val_labels, label_name)
        dst_label = os.path.join(small_val_labels, label_name)
        
        if os.path.exists(src_label):
            shutil.copy2(src_label, dst_label)
    
    print(f"✅ Dataset ridotto creato:")
    print(f"   📁 Training: {len(train_images)} immagini")
    print(f"   📁 Validation: {len(val_images)} immagini")
    print(f"   📂 Percorso: datasets/face_small/")

def create_yaml_config():
    """Crea file YAML per il dataset ridotto"""
    
    yaml_content = """# YOLOv5 Face Detection - Dataset Ridotto (100 immagini)
# Configurazione per training veloce e test

# Percorsi dataset
path: datasets/face_small  # dataset root dir
train: images/train        # train images (relative to 'path')
val: images/val           # val images (relative to 'path')

# Classi
nc: 1  # number of classes
names: ['face']  # class names
"""
    
    with open('datasets/face_small.yaml', 'w') as f:
        f.write(yaml_content)
    
    print("✅ File YAML creato: datasets/face_small.yaml")

if __name__ == "__main__":
    create_small_dataset()
    create_yaml_config()
    print("\n🚀 Dataset ridotto pronto per training veloce!")
    print("💡 Comando training: python train.py --data datasets/face_small.yaml --epochs 10 --batch-size 8")
