#!/usr/bin/env python3
"""
Training script ottimizzato per il dataset migliorato
Con parametri ottimizzati e monitoraggio avanzato
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def check_dataset(dataset_path):
    """Verifica la qualità del dataset"""
    print("🔍 VERIFICA DATASET...")
    
    # Controlla se il dataset esiste
    if not Path(dataset_path).exists():
        print(f"❌ Dataset non trovato: {dataset_path}")
        return False
    
    # Conta immagini e labels
    train_images = len(list(Path(f"{dataset_path}/images/train").glob("*.jpg")))
    val_images = len(list(Path(f"{dataset_path}/images/val").glob("*.jpg")))
    train_labels = len(list(Path(f"{dataset_path}/labels/train").glob("*.txt")))
    val_labels = len(list(Path(f"{dataset_path}/labels/val").glob("*.txt")))
    
    total_images = train_images + val_images
    total_labels = train_labels + val_labels
    
    print(f"📊 STATISTICHE DATASET:")
    print(f"   📚 Training: {train_images} immagini, {train_labels} labels")
    print(f"   🧪 Validation: {val_images} immagini, {val_labels} labels")
    print(f"   🎯 Totale: {total_images} immagini, {total_labels} labels")
    
    # Verifica corrispondenza
    if train_images != train_labels:
        print(f"⚠️  Warning: Mismatch training - {train_images} img vs {train_labels} labels")
    if val_images != val_labels:
        print(f"⚠️  Warning: Mismatch validation - {val_images} img vs {val_labels} labels")
    
    # Raccomandazioni
    if total_images < 100:
        print("⚠️  Dataset piccolo: raccomandato almeno 100 immagini")
        return False
    elif total_images < 200:
        print("💡 Dataset accettabile: per risultati migliori raccomandato 200+ immagini")
    else:
        print("✅ Dataset di buona dimensione!")
    
    return True

def get_optimal_parameters(num_images):
    """Calcola parametri ottimali basati sulla dimensione del dataset"""
    if num_images < 100:
        return {
            'epochs': 50,
            'batch_size': 8,
            'img_size': 416,
            'patience': 20
        }
    elif num_images < 300:
        return {
            'epochs': 100,
            'batch_size': 16,
            'img_size': 640,
            'patience': 30
        }
    else:
        return {
            'epochs': 150,
            'batch_size': 32,
            'img_size': 640,
            'patience': 50
        }

def run_training(dataset_path, model_name="face_improved_v1"):
    """Esegue il training con parametri ottimizzati"""
    
    # Verifica dataset
    if not check_dataset(dataset_path):
        print("❌ Problemi con il dataset. Correggi prima di continuare.")
        return False
    
    # Conta immagini totali
    train_images = len(list(Path(f"{dataset_path}/images/train").glob("*.jpg")))
    val_images = len(list(Path(f"{dataset_path}/images/val").glob("*.jpg")))
    total_images = train_images + val_images
    
    # Parametri ottimali
    params = get_optimal_parameters(total_images)
    
    print(f"\n🚀 CONFIGURAZIONE TRAINING:")
    print(f"   📊 Dataset: {total_images} immagini")
    print(f"   🔄 Epoche: {params['epochs']}")
    print(f"   📦 Batch size: {params['batch_size']}")
    print(f"   📐 Image size: {params['img_size']}")
    print(f"   ⏱️  Patience: {params['patience']}")
    print(f"   🏷️  Nome modello: {model_name}")
    
    # Comando di training
    cmd = [
        "python", "train.py",
        "--data", f"{dataset_path}/dataset.yaml",
        "--weights", "yolov5s.pt",
        "--epochs", str(params['epochs']),
        "--batch-size", str(params['batch_size']),
        "--img", str(params['img_size']),
        "--patience", str(params['patience']),
        "--project", "runs/train",
        "--name", model_name,
        "--save-period", "10",  # Salva ogni 10 epoche
        "--cache",  # Cache per velocità
        "--workers", "8"
    ]
    
    print(f"\n🎯 COMANDO TRAINING:")
    print(" ".join(cmd))
    
    # Conferma utente
    response = input("\n❓ Vuoi procedere con il training? (y/n): ").lower()
    if response != 'y':
        print("❌ Training annullato")
        return False
    
    print("\n🚀 AVVIO TRAINING...")
    print("=" * 60)
    
    # Esegui training
    try:
        start_time = time.time()
        result = subprocess.run(cmd, check=True, capture_output=False)
        end_time = time.time()
        
        training_time = end_time - start_time
        hours = int(training_time // 3600)
        minutes = int((training_time % 3600) // 60)
        
        print("\n" + "=" * 60)
        print("🎉 TRAINING COMPLETATO CON SUCCESSO!")
        print(f"⏱️  Tempo totale: {hours}h {minutes}m")
        print(f"📁 Modello salvato in: runs/train/{model_name}/weights/")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Errore durante il training: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  Training interrotto dall'utente")
        return False

def test_model(model_path):
    """Testa il modello addestrato"""
    print(f"\n🧪 TEST MODELLO: {model_path}")
    
    if not Path(model_path).exists():
        print(f"❌ Modello non trovato: {model_path}")
        return False
    
    # Test su webcam
    cmd = [
        "python", "detect.py",
        "--weights", model_path,
        "--source", "0",  # Webcam
        "--img", "640",
        "--conf", "0.25",
        "--project", "runs/detect",
        "--name", "improved_model_test"
    ]
    
    print("🎯 Test su webcam...")
    print("Comando:", " ".join(cmd))
    
    response = input("❓ Vuoi testare il modello su webcam? (y/n): ").lower()
    if response == 'y':
        try:
            subprocess.run(cmd, check=True)
            print("✅ Test completato")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Errore durante il test: {e}")
            return False
    else:
        print("⏭️  Test saltato")
        return True

def main():
    """Funzione principale"""
    print("🎯 TRAINING MODELLO FACE DETECTION MIGLIORATO")
    print("=" * 60)
    
    # Percorso dataset
    dataset_path = "datasets/face_improved"
    
    # Verifica se il dataset esiste
    if not Path(dataset_path).exists():
        print(f"❌ Dataset non trovato: {dataset_path}")
        print("💡 Prima esegui: python capture_improved_dataset.py")
        return
    
    # Opzioni training
    print("🎛️  OPZIONI TRAINING:")
    print("1. Training standard (parametri automatici)")
    print("2. Training personalizzato")
    print("3. Solo test modello esistente")
    
    choice = input("\n❓ Scegli opzione (1-3): ").strip()
    
    if choice == "1":
        # Training standard
        success = run_training(dataset_path)
        if success:
            model_path = "runs/train/face_improved_v1/weights/best.pt"
            test_model(model_path)
    
    elif choice == "2":
        # Training personalizzato
        model_name = input("📝 Nome modello (default: face_improved_custom): ").strip()
        if not model_name:
            model_name = "face_improved_custom"
        
        success = run_training(dataset_path, model_name)
        if success:
            model_path = f"runs/train/{model_name}/weights/best.pt"
            test_model(model_path)
    
    elif choice == "3":
        # Solo test
        model_path = input("📁 Percorso modello (es: runs/train/face_improved_v1/weights/best.pt): ").strip()
        test_model(model_path)
    
    else:
        print("❌ Opzione non valida")

if __name__ == "__main__":
    main()
