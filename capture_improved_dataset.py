#!/usr/bin/env python3
"""
Cattura dataset migliorato per face detection
Con più controlli, varietà e qualità delle immagini
"""

import cv2
import os
import time
import numpy as np

def create_directories():
    """Crea le directory per il nuovo dataset migliorato"""
    base_dir = "datasets/face_improved"
    dirs = [
        f"{base_dir}/images/train",
        f"{base_dir}/images/val", 
        f"{base_dir}/labels/train",
        f"{base_dir}/labels/val"
    ]
    
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    return base_dir

def detect_faces_multiple_methods(frame):
    """Usa più metodi per rilevare visi e essere più accurati"""
    faces_all = []
    
    # Metodo 1: Haar Cascade frontale
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces1 = face_cascade.detectMultiScale(gray, 1.1, 4, minSize=(30, 30))
    
    # Metodo 2: Haar Cascade profilo
    profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
    faces2 = profile_cascade.detectMultiScale(gray, 1.1, 4, minSize=(30, 30))
    
    # Combina i risultati
    all_faces = list(faces1) + list(faces2)
    
    # Rimuovi duplicati (faces troppo vicine)
    filtered_faces = []
    for face in all_faces:
        x, y, w, h = face
        is_duplicate = False
        for existing in filtered_faces:
            ex, ey, ew, eh = existing
            # Calcola overlap
            overlap_x = max(0, min(x + w, ex + ew) - max(x, ex))
            overlap_y = max(0, min(y + h, ey + eh) - max(y, ey))
            overlap_area = overlap_x * overlap_y
            face_area = w * h
            if overlap_area > face_area * 0.3:  # 30% overlap = duplicato
                is_duplicate = True
                break
        
        if not is_duplicate and w > 30 and h > 30:  # Dimensione minima
            filtered_faces.append(face)
    
    return filtered_faces

def save_yolo_annotation(faces, img_width, img_height, label_path):
    """Salva le annotazioni in formato YOLO"""
    with open(label_path, 'w') as f:
        for (x, y, w, h) in faces:
            # Converti in formato YOLO (normalizzato)
            center_x = (x + w/2) / img_width
            center_y = (y + h/2) / img_height
            width = w / img_width
            height = h / img_height
            
            # Assicurati che i valori siano nel range [0, 1]
            center_x = max(0, min(1, center_x))
            center_y = max(0, min(1, center_y))
            width = max(0, min(1, width))
            height = max(0, min(1, height))
            
            # Classe 0 per face
            f.write(f"0 {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}\n")

def enhance_image_quality(frame):
    """Migliora la qualità dell'immagine"""
    # Equalizzazione istogramma per migliorare contrasto
    lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
    lab[:,:,0] = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8)).apply(lab[:,:,0])
    enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
    
    # Riduzione rumore
    enhanced = cv2.bilateralFilter(enhanced, 9, 75, 75)
    
    return enhanced

def main():
    """Funzione principale per cattura dataset migliorato"""
    print("📸 CATTURA DATASET MIGLIORATO PER YOLOV5")
    print("=" * 60)
    
    # Crea directory
    base_dir = create_directories()
    print(f"✅ Directory create: {base_dir}")
    
    # Inizializza webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Impossibile aprire la webcam")
        return
    
    # Imposta risoluzione più alta per migliore qualità
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    print("✅ Webcam inizializzata (1280x720)")
    print("\n🎯 ISTRUZIONI DETTAGLIATE:")
    print("   - SPAZIO: Cattura immagine (solo se rileva visi)")
    print("   - 'q' o ESC: Esci")
    print("   - 'e': Attiva/disattiva enhancement qualità")
    print("   - 'r': Reset contatori")
    print("\n💡 SUGGERIMENTI PER DATASET MIGLIORE:")
    print("   - Muovi la testa lentamente in tutte le direzioni")
    print("   - Cambia illuminazione (accendi/spegni luci)")
    print("   - Prova diverse espressioni facciali")
    print("   - Avvicinati e allontanati dalla camera")
    print("   - Inclina la testa a destra e sinistra")
    print("   - Prova con e senza occhiali (se li hai)")
    print("   - Target: 200-300 immagini diverse")
    
    # Contatori
    total_captured = 0
    train_count = 0
    val_count = 0
    enhance_mode = True
    
    # Timer per evitare catture troppo rapide
    last_capture_time = 0
    min_capture_interval = 1.0  # 1 secondo tra catture
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip per effetto specchio
            frame = cv2.flip(frame, 1)
            
            # Enhancement qualità se attivato
            display_frame = enhance_image_quality(frame) if enhance_mode else frame.copy()
            
            # Rileva visi con metodi multipli
            faces = detect_faces_multiple_methods(display_frame)
            
            # Disegna i visi rilevati
            for (x, y, w, h) in faces:
                # Colore basato sulla dimensione del viso
                if w * h > 10000:  # Viso grande
                    color = (0, 255, 0)  # Verde
                    quality = "OTTIMA"
                elif w * h > 5000:  # Viso medio
                    color = (0, 255, 255)  # Giallo
                    quality = "BUONA"
                else:  # Viso piccolo
                    color = (0, 0, 255)  # Rosso
                    quality = "PICCOLA"
                
                cv2.rectangle(display_frame, (x, y), (x+w, y+h), color, 2)
                cv2.putText(display_frame, f"Face {quality}", (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
                
                # Dimensioni del viso
                cv2.putText(display_frame, f"{w}x{h}", (x, y+h+20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
            
            # Informazioni principali
            current_time = time.time()
            can_capture = (current_time - last_capture_time) > min_capture_interval
            
            status_color = (0, 255, 0) if len(faces) > 0 and can_capture else (0, 0, 255)
            status_text = f"Faces: {len(faces)} | Captured: {total_captured} | Quality: {'ON' if enhance_mode else 'OFF'}"
            cv2.putText(display_frame, status_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, status_color, 2)
            
            # Statistiche dettagliate
            stats_text = f"Train: {train_count} | Val: {val_count}"
            cv2.putText(display_frame, stats_text, (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Timer per prossima cattura
            if not can_capture:
                remaining = min_capture_interval - (current_time - last_capture_time)
                timer_text = f"Wait: {remaining:.1f}s"
                cv2.putText(display_frame, timer_text, (10, 90), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
            
            # Istruzioni
            if len(faces) > 0 and can_capture:
                instruction = "SPACE to capture (faces detected!)"
                color = (0, 255, 0)
            else:
                instruction = "Move to detect faces or wait for timer"
                color = (255, 255, 255)
            
            cv2.putText(display_frame, instruction, (10, display_frame.shape[0] - 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            cv2.putText(display_frame, "Press 'q' to quit, 'e' for quality, 'r' to reset", 
                       (10, display_frame.shape[0] - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Mostra il frame
            cv2.imshow('Improved Dataset Capture - Face Detection', display_frame)
            
            # Input
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q') or key == 27:  # 'q' o ESC
                break
            elif key == ord('e'):  # Toggle enhancement
                enhance_mode = not enhance_mode
                print(f"🎨 Enhancement qualità: {'ATTIVATO' if enhance_mode else 'DISATTIVATO'}")
            elif key == ord('r'):  # Reset contatori
                total_captured = 0
                train_count = 0
                val_count = 0
                print("🔄 Contatori resettati")
            elif key == ord(' ') and len(faces) > 0 and can_capture:  # SPAZIO e visi rilevati
                # Determina se train o val (80% train, 20% val)
                is_train = (total_captured % 5) != 0  # 4 su 5 per train
                
                if is_train:
                    img_dir = f"{base_dir}/images/train"
                    label_dir = f"{base_dir}/labels/train"
                    train_count += 1
                    subset = "train"
                else:
                    img_dir = f"{base_dir}/images/val"
                    label_dir = f"{base_dir}/labels/val"
                    val_count += 1
                    subset = "val"
                
                # Usa il frame originale (non quello con enhancement per display)
                save_frame = enhance_image_quality(frame) if enhance_mode else frame
                
                # Salva immagine
                img_filename = f"face_{total_captured:04d}.jpg"
                img_path = os.path.join(img_dir, img_filename)
                cv2.imwrite(img_path, save_frame)
                
                # Salva etichette YOLO
                label_filename = f"face_{total_captured:04d}.txt"
                label_path = os.path.join(label_dir, label_filename)
                h, w = save_frame.shape[:2]
                save_yolo_annotation(faces, w, h, label_path)
                
                total_captured += 1
                last_capture_time = current_time
                
                print(f"📸 Catturata #{total_captured}: {img_filename} ({subset}) - {len(faces)} visi - Quality: {'Enhanced' if enhance_mode else 'Normal'}")
                
                # Feedback visivo
                cv2.rectangle(display_frame, (0, 0), (display_frame.shape[1], display_frame.shape[0]), (0, 255, 0), 8)
                cv2.imshow('Improved Dataset Capture - Face Detection', display_frame)
                cv2.waitKey(300)  # Mostra il feedback per 300ms
    
    except KeyboardInterrupt:
        print("\n⏹️  Interruzione da tastiera")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n📊 DATASET MIGLIORATO CREATO:")
        print(f"   📁 Directory: {base_dir}")
        print(f"   🎯 Immagini totali: {total_captured}")
        print(f"   📚 Training: {train_count}")
        print(f"   🧪 Validation: {val_count}")
        if total_captured > 0:
            print(f"   📋 Split: {train_count/total_captured*100:.1f}% train, {val_count/total_captured*100:.1f}% val")
        
        if total_captured > 0:
            # Crea il file yaml per il dataset
            yaml_content = f"""# Improved Face Dataset
path: datasets/face_improved
train: images/train
val: images/val

# Classes
nc: 1  # number of classes
names: ['face']  # class names
"""
            yaml_path = f"{base_dir}/dataset.yaml"
            with open(yaml_path, 'w') as f:
                f.write(yaml_content)
            
            print(f"   ⚙️  Config: {yaml_path}")
            print("\n🚀 PROSSIMO PASSO:")
            print(f"   python train.py --data {yaml_path} --weights yolov5s.pt --epochs 100 --img 640 --batch-size 16")
            print("\n💡 SUGGERIMENTI:")
            print("   - Raccomandato: almeno 200 immagini per buoni risultati")
            print("   - Più varietà = modello più robusto")
            print("   - Qualità > Quantità")

if __name__ == "__main__":
    main()
