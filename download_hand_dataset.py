#!/usr/bin/env python3
"""
Script per scaricare dataset di mani per YOLOv5
Supporta diversi formati e fonti
"""

import os
import requests
import zipfile
import json
from pathlib import Path
import shutil

class HandDatasetDownloader:
    def __init__(self):
        self.dataset_dir = Path("datasets/hands")
        self.temp_dir = Path("temp_download")
        
    def create_directories(self):
        """Crea le directory necessarie"""
        dirs = [
            self.dataset_dir / "images" / "train",
            self.dataset_dir / "images" / "val", 
            self.dataset_dir / "labels" / "train",
            self.dataset_dir / "labels" / "val",
            self.temp_dir
        ]
        
        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def download_roboflow_sample(self):
        """Scarica dataset di esempio da Roboflow"""
        print("🌐 Download dataset Roboflow...")
        
        # URL dataset pubblico di esempio (sostituire con URL reale)
        sample_url = "https://github.com/ultralytics/assets/releases/download/v0.0.0/coco128.zip"
        
        try:
            print("📥 Download in corso...")
            response = requests.get(sample_url, stream=True)
            zip_path = self.temp_dir / "dataset.zip"
            
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("📦 Estrazione...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
            
            print("✅ Download completato!")
            return True
            
        except Exception as e:
            print(f"❌ Errore download: {e}")
            return False
    
    def convert_coco_to_yolo(self, coco_json_path, images_dir):
        """Converte annotazioni COCO in formato YOLO"""
        print("🔄 Conversione COCO → YOLO...")
        
        try:
            with open(coco_json_path, 'r') as f:
                coco_data = json.load(f)
            
            # Estrai solo annotazioni di mani/persone
            hand_annotations = []
            for ann in coco_data.get('annotations', []):
                # Filtra per categoria "person" o simili
                if ann.get('category_id') == 1:  # person in COCO
                    hand_annotations.append(ann)
            
            print(f"📊 Trovate {len(hand_annotations)} annotazioni")
            return True
            
        except Exception as e:
            print(f"❌ Errore conversione: {e}")
            return False
    
    def validate_dataset_format(self):
        """Valida che il dataset sia nel formato corretto"""
        print("✅ Validazione formato dataset...")
        
        train_images = list((self.dataset_dir / "images" / "train").glob("*"))
        train_labels = list((self.dataset_dir / "labels" / "train").glob("*.txt"))
        val_images = list((self.dataset_dir / "images" / "val").glob("*"))
        val_labels = list((self.dataset_dir / "labels" / "val").glob("*.txt"))
        
        print(f"📊 Training: {len(train_images)} immagini, {len(train_labels)} labels")
        print(f"📊 Validation: {len(val_images)} immagini, {len(val_labels)} labels")
        
        # Controlla corrispondenza immagini-labels
        issues = []
        for img_path in train_images:
            label_path = self.dataset_dir / "labels" / "train" / f"{img_path.stem}.txt"
            if not label_path.exists():
                issues.append(f"Missing label: {label_path}")
        
        if issues:
            print("⚠️ Problemi trovati:")
            for issue in issues[:5]:  # Mostra solo primi 5
                print(f"   {issue}")
        else:
            print("✅ Dataset formato corretto!")
        
        return len(issues) == 0
    
    def show_download_instructions(self):
        """Mostra istruzioni per download manuale"""
        print("\n📋 ISTRUZIONI DOWNLOAD MANUALE")
        print("=" * 50)
        
        print("\n🎯 OPZIONE 1: Roboflow Universe (Raccomandato)")
        print("1. Vai su: https://universe.roboflow.com")
        print("2. Cerca: 'hand detection'")
        print("3. Scegli dataset con 1000+ immagini")
        print("4. Download formato: 'YOLOv5 PyTorch'")
        print("5. Estrai in: datasets/hands/")
        
        print("\n🎯 OPZIONE 2: Kaggle")
        print("1. Vai su: https://www.kaggle.com/datasets")
        print("2. Cerca: 'hand detection dataset'")
        print("3. Download e converti in formato YOLO")
        
        print("\n🎯 OPZIONE 3: GitHub")
        print("Repository consigliati:")
        print("- https://github.com/cansik/yolo-hand-detection")
        print("- https://github.com/victordibia/handtracking")
        
        print("\n📁 STRUTTURA RICHIESTA:")
        print("datasets/hands/")
        print("├── images/")
        print("│   ├── train/     # File .jpg, .png")
        print("│   └── val/")
        print("├── labels/")
        print("│   ├── train/     # File .txt (formato YOLO)")
        print("│   └── val/")
        print("└── data.yaml")
        
        print("\n📝 FORMATO LABEL (.txt):")
        print("class_id x_center y_center width height")
        print("Esempio: 0 0.5 0.3 0.2 0.4")
        print("(tutti valori normalizzati 0-1)")
        
        print("\n🔧 TOOL CONVERSIONE:")
        print("- Roboflow: conversione automatica")
        print("- LabelImg: annotazione manuale") 
        print("- CVAT: annotazione professionale")
    
    def create_sample_download_script(self):
        """Crea script per download specifici"""
        script_content = '''#!/usr/bin/env python3
"""
Script per download dataset specifici
Personalizza gli URL per i tuoi dataset preferiti
"""

import requests
import zipfile
from pathlib import Path

def download_dataset(url, extract_to):
    """Download e estrazione dataset"""
    print(f"📥 Download da: {url}")
    
    try:
        response = requests.get(url, stream=True)
        zip_path = "temp_dataset.zip"
        
        with open(zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print("📦 Estrazione...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        
        Path(zip_path).unlink()  # Rimuovi zip
        print("✅ Download completato!")
        
    except Exception as e:
        print(f"❌ Errore: {e}")

# ESEMPI URL DATASET (sostituire con URL reali)
datasets = {
    "roboflow_hands": "URL_ROBOFLOW_DATASET",
    "kaggle_hands": "URL_KAGGLE_DATASET", 
    "github_hands": "URL_GITHUB_DATASET"
}

if __name__ == "__main__":
    print("🖐️ Hand Dataset Downloader")
    print("Modifica gli URL nel codice con dataset reali")
    
    for name, url in datasets.items():
        if url != f"URL_{name.upper()}_DATASET":
            download_dataset(url, f"datasets/hands_{name}")
'''
        
        script_path = self.dataset_dir / "download_specific.py"
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        print(f"📜 Script creato: {script_path}")
    
    def run(self):
        """Esegue il processo di download"""
        print("🖐️ Hand Dataset Downloader")
        print("=" * 40)
        
        self.create_directories()
        
        print("\nScegli opzione:")
        print("1. 📋 Mostra istruzioni download manuale")
        print("2. 🔍 Valida dataset esistente")
        print("3. 📜 Crea script download personalizzato")
        print("4. 🧹 Pulisci dataset di esempio")
        
        choice = input("\nScelta (1-4): ").strip()
        
        if choice == "1":
            self.show_download_instructions()
        elif choice == "2":
            self.validate_dataset_format()
        elif choice == "3":
            self.create_sample_download_script()
        elif choice == "4":
            self.cleanup_sample_data()
        else:
            print("❌ Scelta non valida")
    
    def cleanup_sample_data(self):
        """Rimuove i dati di esempio"""
        print("🧹 Rimozione dati di esempio...")
        
        try:
            # Rimuovi immagini di esempio
            for img_path in (self.dataset_dir / "images" / "train").glob("hand_*.jpg"):
                img_path.unlink()
            for img_path in (self.dataset_dir / "images" / "val").glob("hand_*.jpg"):
                img_path.unlink()
            
            # Rimuovi labels di esempio  
            for label_path in (self.dataset_dir / "labels" / "train").glob("hand_*.txt"):
                label_path.unlink()
            for label_path in (self.dataset_dir / "labels" / "val").glob("hand_*.txt"):
                label_path.unlink()
            
            print("✅ Dati di esempio rimossi!")
            print("📁 Directory pronte per dataset reali")
            
        except Exception as e:
            print(f"❌ Errore pulizia: {e}")

if __name__ == "__main__":
    downloader = HandDatasetDownloader()
    downloader.run()
