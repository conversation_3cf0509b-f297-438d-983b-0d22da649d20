#!/usr/bin/env python3
"""
YOLOv5 Face Detection Real-time Webcam Test
Addestrato con successo su dataset WIDER FACE
"""

import cv2
import torch
import numpy as np
from pathlib import Path
import time

def load_model():
    """Carica il modello YOLOv5 Face Detection addestrato"""
    model_path = "runs/train/face_detection_final/weights/best.pt"
    
    # Carica il modello
    model = torch.hub.load('ultralytics/yolov5', 'custom', path=model_path, force_reload=True)
    model.conf = 0.25  # Soglia di confidenza
    model.iou = 0.45   # Soglia IoU per NMS
    
    print(f"✅ Modello caricato: {model_path}")
    print(f"📊 Confidenza: {model.conf}")
    print(f"🎯 IoU threshold: {model.iou}")
    
    return model

def main():
    """Funzione principale per test webcam real-time"""
    print("🎥 Avvio YOLOv5 Face Detection Real-time...")
    
    # Carica il modello
    try:
        model = load_model()
    except Exception as e:
        print(f"❌ Errore caricamento modello: {e}")
        return
    
    # Inizializza la webcam
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ Impossibile accedere alla webcam")
        print("💡 Suggerimenti:")
        print("   - Verifica che la webcam sia connessa")
        print("   - Chiudi altre applicazioni che usano la webcam")
        print("   - Prova con un indice diverso (1, 2, etc.)")
        return
    
    # Imposta risoluzione webcam
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("✅ Webcam inizializzata")
    print("🎯 Premi 'q' per uscire")
    
    # Variabili per FPS
    fps_counter = 0
    start_time = time.time()
    
    while True:
        # Cattura frame
        ret, frame = cap.read()
        if not ret:
            print("❌ Impossibile leggere frame dalla webcam")
            break
        
        # Inference YOLOv5
        results = model(frame)
        
        # Estrai risultati
        detections = results.pandas().xyxy[0]
        
        # Disegna bounding boxes
        for _, detection in detections.iterrows():
            x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
            confidence = detection['confidence']
            
            # Disegna rettangolo
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Aggiungi testo confidenza
            label = f"Face: {confidence:.2f}"
            cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Calcola e mostra FPS
        fps_counter += 1
        elapsed_time = time.time() - start_time
        if elapsed_time >= 1.0:
            fps = fps_counter / elapsed_time
            fps_counter = 0
            start_time = time.time()
            
            # Mostra info su frame
            cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(frame, f"Faces: {len(detections)}", (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Mostra frame
        cv2.imshow('YOLOv5 Face Detection - Real Time', frame)
        
        # Esci con 'q'
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("🎥 Webcam chiusa")

if __name__ == "__main__":
    main()
