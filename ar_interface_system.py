#!/usr/bin/env python3
"""
AR INTERFACE SYSTEM - Interfaccia di Realtà Aumentata
Componenti:
1. Virtual Keyboard - Tastiera virtuale nell'aria
2. Holographic Menus - Menu 3D navigabili
3. Object Manipulation - Manipolazione oggetti virtuali
4. Gesture Navigation - Navigazione spaziale 3D
"""

import cv2
import numpy as np
import mediapipe as mp
import math
import time
from collections import deque
import json

class ARInterface:
    def __init__(self):
        # MediaPipe setup
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        # AR Interface state
        self.current_mode = "menu"  # menu, keyboard, object, navigation
        self.frame_width = 1280
        self.frame_height = 720
        
        # Virtual Keyboard
        self.keyboard_layout = self.create_keyboard_layout()
        self.typed_text = ""
        self.last_key_time = 0
        
        # Holographic Menu
        self.menu_items = [
            {"name": "Keyboard", "mode": "keyboard", "pos": (200, 200), "size": 120},
            {"name": "Objects", "mode": "object", "pos": (400, 200), "size": 120},
            {"name": "Navigate", "mode": "navigation", "pos": (600, 200), "size": 120},
            {"name": "Settings", "mode": "settings", "pos": (800, 200), "size": 120}
        ]
        self.selected_menu = None
        
        # Virtual Objects
        self.virtual_objects = [
            {"name": "Cube", "pos": [400, 300, 0], "size": 80, "rotation": 0, "color": (0, 255, 0)},
            {"name": "Sphere", "pos": [600, 300, 0], "size": 60, "rotation": 0, "color": (255, 0, 0)},
            {"name": "Pyramid", "pos": [800, 300, 0], "size": 70, "rotation": 0, "color": (0, 0, 255)}
        ]
        self.selected_object = None
        self.manipulation_mode = "move"  # move, rotate, scale
        
        # Navigation 3D
        self.camera_pos = [0, 0, 0]
        self.camera_rotation = [0, 0, 0]
        self.navigation_sensitivity = 2.0
        
        # Gesture tracking
        self.gesture_history = deque(maxlen=10)
        self.pinch_threshold = 40
        self.last_gesture_time = 0
        
        print("✅ AR Interface System inizializzato")
        print("🎯 Modalità disponibili: Menu, Keyboard, Objects, Navigation")
    
    def create_keyboard_layout(self):
        """Crea layout tastiera virtuale QWERTY"""
        keyboard = {
            'row1': ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
            'row2': ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
            'row3': ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],
            'row4': ['SPACE', 'BACKSPACE', 'ENTER']
        }
        
        # Calcola posizioni
        layout = {}
        start_x, start_y = 100, 400
        key_width, key_height = 80, 60
        
        for row_idx, (row_name, keys) in enumerate(keyboard.items()):
            row_offset = row_idx * 20  # Offset per layout staggered
            for key_idx, key in enumerate(keys):
                x = start_x + key_idx * (key_width + 10) + row_offset
                y = start_y + row_idx * (key_height + 10)
                
                # Tasti speciali più larghi
                width = key_width * 2 if key in ['SPACE', 'BACKSPACE', 'ENTER'] else key_width
                
                layout[key] = {
                    'pos': (x, y),
                    'size': (width, key_height),
                    'pressed': False
                }
        
        return layout
    
    def calculate_distance(self, point1, point2):
        """Calcola distanza euclidea"""
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def detect_pinch(self, landmarks):
        """Rileva gesto pinch"""
        thumb_tip = landmarks.landmark[4]
        index_tip = landmarks.landmark[8]
        
        thumb_pos = (int(thumb_tip.x * self.frame_width), int(thumb_tip.y * self.frame_height))
        index_pos = (int(index_tip.x * self.frame_width), int(index_tip.y * self.frame_height))
        
        distance = self.calculate_distance(thumb_pos, index_pos)
        return distance < self.pinch_threshold, distance, index_pos
    
    def detect_point_gesture(self, landmarks):
        """Rileva gesto di puntamento"""
        index_tip = landmarks.landmark[8]
        index_pip = landmarks.landmark[6]
        middle_tip = landmarks.landmark[12]
        
        # Indice esteso, medio piegato
        index_extended = index_tip.y < index_pip.y
        middle_folded = middle_tip.y > landmarks.landmark[10].y
        
        if index_extended and middle_folded:
            pos = (int(index_tip.x * self.frame_width), int(index_tip.y * self.frame_height))
            return True, pos
        
        return False, None
    
    def process_menu_mode(self, hands_data):
        """Processa modalità menu olografico"""
        for hand_data in hands_data:
            is_pointing, point_pos = self.detect_point_gesture(hand_data['landmarks'])
            is_pinch, pinch_dist, pinch_pos = self.detect_pinch(hand_data['landmarks'])
            
            if is_pointing and point_pos:
                # Controlla hover sui menu items
                for item in self.menu_items:
                    item_x, item_y = item['pos']
                    item_size = item['size']
                    
                    if (item_x <= point_pos[0] <= item_x + item_size and 
                        item_y <= point_pos[1] <= item_y + item_size):
                        self.selected_menu = item['name']
                        
                        # Selezione con pinch
                        if is_pinch and time.time() - self.last_gesture_time > 1.0:
                            self.current_mode = item['mode']
                            self.last_gesture_time = time.time()
                            print(f"🎯 Modalità cambiata: {item['mode'].upper()}")
                            break
    
    def process_keyboard_mode(self, hands_data):
        """Processa modalità tastiera virtuale"""
        for hand_data in hands_data:
            is_pinch, pinch_dist, pinch_pos = self.detect_pinch(hand_data['landmarks'])
            
            if is_pinch and time.time() - self.last_key_time > 0.5:
                # Controlla quale tasto è stato premuto
                for key, key_data in self.keyboard_layout.items():
                    key_x, key_y = key_data['pos']
                    key_w, key_h = key_data['size']
                    
                    if (key_x <= pinch_pos[0] <= key_x + key_w and 
                        key_y <= pinch_pos[1] <= key_y + key_h):
                        
                        # Gestisci tasti speciali
                        if key == 'SPACE':
                            self.typed_text += ' '
                        elif key == 'BACKSPACE':
                            self.typed_text = self.typed_text[:-1]
                        elif key == 'ENTER':
                            print(f"📝 Testo digitato: '{self.typed_text}'")
                            self.typed_text = ""
                        else:
                            self.typed_text += key.lower()
                        
                        self.last_key_time = time.time()
                        key_data['pressed'] = True
                        print(f"⌨️ Tasto premuto: {key}")
                        break
    
    def process_object_mode(self, hands_data):
        """Processa modalità manipolazione oggetti"""
        if len(hands_data) >= 1:
            hand = hands_data[0]
            is_pinch, pinch_dist, pinch_pos = self.detect_pinch(hand['landmarks'])
            
            # Selezione oggetto
            if is_pinch and not self.selected_object:
                for obj in self.virtual_objects:
                    obj_x, obj_y = obj['pos'][:2]
                    obj_size = obj['size']
                    
                    if (obj_x - obj_size//2 <= pinch_pos[0] <= obj_x + obj_size//2 and 
                        obj_y - obj_size//2 <= pinch_pos[1] <= obj_y + obj_size//2):
                        self.selected_object = obj
                        print(f"🎯 Oggetto selezionato: {obj['name']}")
                        break
            
            # Manipolazione oggetto selezionato
            elif self.selected_object:
                if is_pinch:
                    # Movimento
                    if self.manipulation_mode == "move":
                        self.selected_object['pos'][0] = pinch_pos[0]
                        self.selected_object['pos'][1] = pinch_pos[1]
                    
                    # Rotazione (basata su movimento X)
                    elif self.manipulation_mode == "rotate":
                        self.selected_object['rotation'] += (pinch_pos[0] - self.frame_width//2) * 0.01
                    
                    # Scala (basata su distanza pinch)
                    elif self.manipulation_mode == "scale":
                        scale_factor = max(0.5, min(2.0, pinch_dist / 50))
                        self.selected_object['size'] = int(80 * scale_factor)
                
                else:
                    # Rilascia oggetto
                    self.selected_object = None
    
    def process_navigation_mode(self, hands_data):
        """Processa modalità navigazione 3D"""
        if len(hands_data) >= 1:
            hand = hands_data[0]
            landmarks = hand['landmarks']
            
            # Usa posizione della mano per navigazione
            palm_center = landmarks.landmark[9]  # Centro palmo
            palm_x = palm_center.x * self.frame_width
            palm_y = palm_center.y * self.frame_height
            
            # Movimento camera basato su posizione palmo
            self.camera_pos[0] = (palm_x - self.frame_width//2) * self.navigation_sensitivity
            self.camera_pos[1] = (palm_y - self.frame_height//2) * self.navigation_sensitivity
            
            # Rotazione con orientamento mano
            wrist = landmarks.landmark[0]
            middle_mcp = landmarks.landmark[9]
            
            angle = math.atan2(middle_mcp.y - wrist.y, middle_mcp.x - wrist.x)
            self.camera_rotation[2] = math.degrees(angle)
    
    def draw_holographic_menu(self, frame):
        """Disegna menu olografico 3D"""
        for item in self.menu_items:
            x, y = item['pos']
            size = item['size']
            name = item['name']
            
            # Effetto olografico
            color = (0, 255, 255) if self.selected_menu == name else (100, 200, 255)
            
            # Bordo esterno (effetto glow)
            cv2.rectangle(frame, (x-5, y-5), (x+size+5, y+size+5), (50, 150, 255), 2)
            
            # Pannello principale
            cv2.rectangle(frame, (x, y), (x+size, y+size), color, -1)
            cv2.rectangle(frame, (x, y), (x+size, y+size), (255, 255, 255), 2)
            
            # Testo
            text_size = cv2.getTextSize(name, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            text_x = x + (size - text_size[0]) // 2
            text_y = y + (size + text_size[1]) // 2
            
            cv2.putText(frame, name, (text_x, text_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        return frame
    
    def draw_virtual_keyboard(self, frame):
        """Disegna tastiera virtuale"""
        for key, key_data in self.keyboard_layout.items():
            x, y = key_data['pos']
            w, h = key_data['size']
            pressed = key_data.get('pressed', False)
            
            # Reset pressed state
            if pressed:
                key_data['pressed'] = False
            
            # Colore tasto
            color = (0, 255, 0) if pressed else (200, 200, 200)
            border_color = (255, 255, 255)
            
            # Disegna tasto
            cv2.rectangle(frame, (x, y), (x+w, y+h), color, -1)
            cv2.rectangle(frame, (x, y), (x+w, y+h), border_color, 2)
            
            # Testo tasto
            text_size = cv2.getTextSize(key, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            text_x = x + (w - text_size[0]) // 2
            text_y = y + (h + text_size[1]) // 2
            
            cv2.putText(frame, key, (text_x, text_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
        
        # Mostra testo digitato
        cv2.putText(frame, f"Testo: {self.typed_text}", (100, 350), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        
        return frame
    
    def draw_virtual_objects(self, frame):
        """Disegna oggetti virtuali manipolabili"""
        for obj in self.virtual_objects:
            x, y, z = obj['pos']
            size = obj['size']
            rotation = obj['rotation']
            color = obj['color']
            name = obj['name']
            
            # Evidenzia oggetto selezionato
            if obj == self.selected_object:
                cv2.circle(frame, (x, y), size + 20, (255, 255, 0), 3)
            
            # Disegna oggetto basato sul tipo
            if name == "Cube":
                # Cubo con rotazione
                points = []
                for dx in [-size//2, size//2]:
                    for dy in [-size//2, size//2]:
                        # Rotazione semplificata
                        rx = dx * math.cos(rotation) - dy * math.sin(rotation)
                        ry = dx * math.sin(rotation) + dy * math.cos(rotation)
                        points.append((int(x + rx), int(y + ry)))
                
                cv2.fillPoly(frame, [np.array(points)], color)
                cv2.polylines(frame, [np.array(points)], True, (255, 255, 255), 2)
            
            elif name == "Sphere":
                cv2.circle(frame, (x, y), size//2, color, -1)
                cv2.circle(frame, (x, y), size//2, (255, 255, 255), 2)
            
            elif name == "Pyramid":
                points = np.array([
                    [x, y - size//2],  # Top
                    [x - size//2, y + size//2],  # Bottom left
                    [x + size//2, y + size//2]   # Bottom right
                ])
                cv2.fillPoly(frame, [points], color)
                cv2.polylines(frame, [points], True, (255, 255, 255), 2)
            
            # Nome oggetto
            cv2.putText(frame, name, (x - 30, y + size//2 + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Modalità manipolazione
        mode_text = f"Modalità: {self.manipulation_mode.upper()}"
        cv2.putText(frame, mode_text, (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        return frame
    
    def draw_navigation_3d(self, frame):
        """Disegna interfaccia navigazione 3D"""
        # Griglia 3D simulata
        grid_size = 50
        for i in range(0, self.frame_width, grid_size):
            for j in range(0, self.frame_height, grid_size):
                # Applica trasformazione camera
                x = i + int(self.camera_pos[0] * 0.1)
                y = j + int(self.camera_pos[1] * 0.1)
                
                if 0 <= x < self.frame_width and 0 <= y < self.frame_height:
                    cv2.circle(frame, (x, y), 2, (100, 100, 100), -1)
        
        # Informazioni camera
        info_text = [
            f"Camera X: {self.camera_pos[0]:.1f}",
            f"Camera Y: {self.camera_pos[1]:.1f}",
            f"Rotazione: {self.camera_rotation[2]:.1f}°"
        ]
        
        for i, text in enumerate(info_text):
            cv2.putText(frame, text, (50, 50 + i * 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        return frame

    def process_hands(self, frame):
        """Processa detection mani e gesti"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_frame)

        hands_data = []

        if results.multi_hand_landmarks:
            for hand_idx, hand_landmarks in enumerate(results.multi_hand_landmarks):
                # Info mano
                hand_label = results.multi_handedness[hand_idx].classification[0].label
                confidence = results.multi_handedness[hand_idx].classification[0].score

                hands_data.append({
                    'landmarks': hand_landmarks,
                    'label': hand_label,
                    'confidence': confidence
                })

                # Disegna landmarks
                self.mp_drawing.draw_landmarks(
                    frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS,
                    self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                    self.mp_drawing.DrawingSpec(color=(255, 255, 255), thickness=2)
                )

        return hands_data
