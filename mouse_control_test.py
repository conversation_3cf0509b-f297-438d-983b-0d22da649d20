#!/usr/bin/env python3
"""
Test Controllo Mouse Virtuale con Gesti
ATTENZIONE: Questo script controlla il mouse del sistema!
"""

import cv2
import mediapipe as mp
import pyautogui
import math
import time
from collections import deque

class MouseController:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,  # Solo una mano per controllo mouse
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        # Configurazioni mouse
        self.screen_width, self.screen_height = pyautogui.size()
        self.mouse_smoothing = deque(maxlen=8)  # <PERSON>ù smoothing
        self.click_threshold = 35  # Soglia più sensibile
        self.last_click_time = 0
        self.mouse_enabled = False  # Disabilitato di default

        # Zone di controllo (evita bordi schermo)
        self.margin = 50
        self.control_width = self.screen_width - (2 * self.margin)
        self.control_height = self.screen_height - (2 * self.margin)
        
        # Sicurezza
        pyautogui.FAILSAFE = True  # Muovi mouse in angolo per fermare
        pyautogui.PAUSE = 0.01  # Pausa minima tra comandi

        # Modalità avanzate
        self.scroll_mode = False
        self.drag_mode = False
        self.last_scroll_time = 0

        # Opzioni movimento
        self.mirror_x = False  # Movimento specchiato X
        self.sensitivity = 1.0  # Sensibilità movimento
        self.movement_mode = "normal"  # normal, precise, fast

        print("✅ Mouse Controller inizializzato")
        print(f"🖥️ Risoluzione schermo: {self.screen_width}x{self.screen_height}")
        print(f"📐 Zona controllo: {self.control_width}x{self.control_height}")
        print("⚠️ SICUREZZA: Muovi mouse in angolo superiore sinistro per fermare")
    
    def calculate_distance(self, point1, point2):
        """Calcola distanza tra due punti"""
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def detect_pinch(self, landmarks):
        """Rileva gesto pinch per click"""
        thumb_tip = landmarks.landmark[4]
        index_tip = landmarks.landmark[8]

        # Converti in coordinate pixel
        h, w = 480, 640
        thumb_px = (int(thumb_tip.x * w), int(thumb_tip.y * h))
        index_px = (int(index_tip.x * w), int(index_tip.y * h))

        distance = self.calculate_distance(thumb_px, index_px)
        return distance < self.click_threshold

    def detect_scroll_gesture(self, landmarks):
        """Rileva gesto scroll (due dita verticali)"""
        index_tip = landmarks.landmark[8]
        middle_tip = landmarks.landmark[12]

        # Converti in coordinate pixel
        h, w = 480, 640
        index_px = (int(index_tip.x * w), int(index_tip.y * h))
        middle_px = (int(middle_tip.x * w), int(middle_tip.y * h))

        # Distanza tra indice e medio
        distance = self.calculate_distance(index_px, middle_px)

        # Se le dita sono vicine e verticali = scroll
        if distance < 60:
            # Calcola direzione scroll basata su movimento Y
            avg_y = (index_tip.y + middle_tip.y) / 2
            return True, avg_y

        return False, 0

    def detect_right_click(self, landmarks):
        """Rileva gesto right click (tre dita vicine)"""
        thumb_tip = landmarks.landmark[4]
        index_tip = landmarks.landmark[8]
        middle_tip = landmarks.landmark[12]

        h, w = 480, 640
        thumb_px = (int(thumb_tip.x * w), int(thumb_tip.y * h))
        index_px = (int(index_tip.x * w), int(index_tip.y * h))
        middle_px = (int(middle_tip.x * w), int(middle_tip.y * h))

        # Tutte e tre le dita vicine
        dist1 = self.calculate_distance(thumb_px, index_px)
        dist2 = self.calculate_distance(index_px, middle_px)

        return dist1 < 45 and dist2 < 45
    
    def control_mouse(self, landmarks):
        """Controlla mouse con posizione indice migliorato"""
        if not self.mouse_enabled:
            return False, 0

        # Usa punta dell'indice per posizione mouse
        index_tip = landmarks.landmark[8]

        # Normalizza coordinate alla zona di controllo (evita bordi)
        # Applica opzioni movimento
        norm_x = (1 - index_tip.x) if self.mirror_x else index_tip.x
        norm_y = index_tip.y

        # Applica sensibilità
        norm_x = norm_x * self.sensitivity
        norm_y = norm_y * self.sensitivity

        # Limita ai bordi
        norm_x = max(0, min(1, norm_x))
        norm_y = max(0, min(1, norm_y))

        # Mappa alla zona di controllo (con margini)
        screen_x = int(self.margin + (norm_x * self.control_width))
        screen_y = int(self.margin + (norm_y * self.control_height))

        # Limita ai bordi schermo
        screen_x = max(self.margin, min(self.screen_width - self.margin, screen_x))
        screen_y = max(self.margin, min(self.screen_height - self.margin, screen_y))

        # Smoothing del movimento
        self.mouse_smoothing.append((screen_x, screen_y))

        if len(self.mouse_smoothing) > 0:
            # Media mobile pesata (più peso ai valori recenti)
            weights = [i + 1 for i in range(len(self.mouse_smoothing))]
            total_weight = sum(weights)

            avg_x = sum(pos[0] * weight for pos, weight in zip(self.mouse_smoothing, weights)) // total_weight
            avg_y = sum(pos[1] * weight for pos, weight in zip(self.mouse_smoothing, weights)) // total_weight

            try:
                # Durata movimento basata su modalità
                duration_map = {
                    "precise": 0.1,
                    "normal": 0.05,
                    "fast": 0.01
                }
                duration = duration_map.get(self.movement_mode, 0.05)

                # Muovi mouse con movimento fluido
                pyautogui.moveTo(avg_x, avg_y, duration=duration)

                # Rileva vari gesti
                is_pinch = self.detect_pinch(landmarks)
                is_scroll, scroll_y = self.detect_scroll_gesture(landmarks)
                is_right_click = self.detect_right_click(landmarks)

                pinch_distance = self.calculate_distance(
                    (int(landmarks.landmark[4].x * 640), int(landmarks.landmark[4].y * 480)),
                    (int(landmarks.landmark[8].x * 640), int(landmarks.landmark[8].y * 480))
                )

                current_time = time.time()

                # Right click (priorità alta)
                if is_right_click and current_time - self.last_click_time > 1.0:
                    pyautogui.rightClick()
                    self.last_click_time = current_time
                    print("🖱️ RIGHT CLICK!")
                    return "right_click", pinch_distance

                # Left click con pinch
                elif is_pinch and current_time - self.last_click_time > 0.8:
                    pyautogui.click()
                    self.last_click_time = current_time
                    print("🖱️ LEFT CLICK!")
                    return "left_click", pinch_distance

                # Scroll con due dita
                elif is_scroll and current_time - self.last_scroll_time > 0.1:
                    # Scroll basato su posizione Y
                    if scroll_y < 0.4:  # Parte alta = scroll up
                        pyautogui.scroll(3)
                        print("🖱️ SCROLL UP!")
                    elif scroll_y > 0.6:  # Parte bassa = scroll down
                        pyautogui.scroll(-3)
                        print("🖱️ SCROLL DOWN!")
                    self.last_scroll_time = current_time
                    return "scroll", pinch_distance

                return "move", pinch_distance

            except pyautogui.FailSafeException:
                print("🛑 FAILSAFE ATTIVATO - Mouse fermato!")
                self.mouse_enabled = False
                return False, 0

        return False, 0
    
    def toggle_mouse(self):
        """Attiva/disattiva controllo mouse"""
        self.mouse_enabled = not self.mouse_enabled
        status = "ATTIVO" if self.mouse_enabled else "DISATTIVO"
        print(f"🖱️ Controllo mouse: {status}")

        if self.mouse_enabled:
            print("⚠️ ATTENZIONE: Mouse virtuale attivo!")
            print("   - Usa INDICE per muovere cursore")
            print("   - Usa PINCH (pollice+indice) per click")
            print("   - Muovi mouse in angolo per FERMARE")

    def toggle_mirror_x(self):
        """Attiva/disattiva specchio X"""
        self.mirror_x = not self.mirror_x
        status = "SPECCHIATO" if self.mirror_x else "NORMALE"
        print(f"🔄 Movimento X: {status}")

    def change_sensitivity(self, delta=0.1):
        """Cambia sensibilità movimento"""
        self.sensitivity += delta
        self.sensitivity = max(0.1, min(3.0, self.sensitivity))
        print(f"🎯 Sensibilità: {self.sensitivity:.1f}x")

    def cycle_movement_mode(self):
        """Cambia modalità movimento"""
        modes = ["precise", "normal", "fast"]
        current_idx = modes.index(self.movement_mode)
        self.movement_mode = modes[(current_idx + 1) % len(modes)]
        print(f"⚡ Modalità movimento: {self.movement_mode.upper()}")
    
    def process_frame(self, frame):
        """Processa frame per controllo mouse"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_frame)
        
        hand_data = None
        action = "none"
        pinch_distance = 0

        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Usa solo la prima mano rilevata
                action, pinch_distance = self.control_mouse(hand_landmarks)

                hand_data = {
                    'landmarks': hand_landmarks,
                    'action': action,
                    'pinch_distance': pinch_distance
                }
                break  # Solo una mano
        
        return hand_data
    
    def draw_mouse_control(self, frame, hand_data):
        """Disegna visualizzazione controllo mouse"""
        if hand_data:
            landmarks = hand_data['landmarks']
            action = hand_data['action']
            pinch_distance = hand_data['pinch_distance']
            
            # Disegna landmarks
            self.mp_drawing.draw_landmarks(
                frame, landmarks, self.mp_hands.HAND_CONNECTIONS,
                self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                self.mp_drawing.DrawingSpec(color=(255, 255, 255), thickness=2)
            )
            
            # Evidenzia indice (controllo mouse)
            index_tip = landmarks.landmark[8]
            h, w, _ = frame.shape
            index_x, index_y = int(index_tip.x * w), int(index_tip.y * h)
            
            # Colore basato sull'azione
            action_colors = {
                "left_click": (0, 0, 255),    # Rosso
                "right_click": (255, 0, 255), # Magenta
                "scroll": (0, 255, 255),      # Giallo
                "move": (255, 0, 0)           # Blu
            }
            color = action_colors.get(action, (255, 0, 0))

            # Cerchio su indice
            cv2.circle(frame, (index_x, index_y), 12, color, -1)
            cv2.circle(frame, (index_x, index_y), 15, (255, 255, 255), 2)

            # Evidenzia pollice (per pinch)
            thumb_tip = landmarks.landmark[4]
            thumb_x, thumb_y = int(thumb_tip.x * w), int(thumb_tip.y * h)
            cv2.circle(frame, (thumb_x, thumb_y), 8, (0, 255, 255), -1)

            # Evidenzia medio (per scroll)
            middle_tip = landmarks.landmark[12]
            middle_x, middle_y = int(middle_tip.x * w), int(middle_tip.y * h)
            cv2.circle(frame, (middle_x, middle_y), 6, (255, 255, 0), -1)

            # Linee di connessione
            cv2.line(frame, (thumb_x, thumb_y), (index_x, index_y), (255, 255, 0), 2)
            cv2.line(frame, (index_x, index_y), (middle_x, middle_y), (0, 255, 255), 2)

            # Testo distanza pinch
            cv2.putText(frame, f"Pinch: {pinch_distance:.1f}", (10, 120),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

            # Stato azione
            action_text = {
                "left_click": "LEFT CLICK!",
                "right_click": "RIGHT CLICK!",
                "scroll": "SCROLLING!",
                "move": "MOVING"
            }.get(action, "IDLE")

            action_color = action_colors.get(action, (255, 255, 255))
            cv2.putText(frame, action_text, (10, 160),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, action_color, 3)
        
        return frame

def main():
    print("🖱️ TEST CONTROLLO MOUSE VIRTUALE")
    print("=" * 50)
    print("⚠️ ATTENZIONE: Questo script controlla il mouse!")
    print("🛑 SICUREZZA: Muovi mouse in angolo superiore sinistro per fermare")
    print("=" * 50)
    
    # Inizializza controller
    controller = MouseController()
    
    # Inizializza webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Impossibile aprire la webcam")
        return
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("✅ Webcam inizializzata")
    print("\n📋 COMANDI:")
    print("   - 'm': Attiva/disattiva controllo mouse")
    print("   - 'x': Toggle specchio movimento X")
    print("   - '+': Aumenta sensibilità")
    print("   - '-': Diminuisci sensibilità")
    print("   - 'v': Cambia velocità (precise/normal/fast)")
    print("   - 'q': Esci")
    print("   - 's': Screenshot")
    print("\n🎯 CONTROLLI MOUSE AVANZATI:")
    print("   - 👆 INDICE: Muove cursore")
    print("   - 🤏 PINCH: Left click (pollice + indice)")
    print("   - 🖱️ TRE DITA: Right click (pollice + indice + medio)")
    print("   - 📜 DUE DITA: Scroll (indice + medio)")
    print("     • Alto = Scroll UP")
    print("     • Basso = Scroll DOWN")
    
    # Statistiche
    fps_counter = 0
    fps_start_time = time.time()
    current_fps = 0
    action_counts = {
        "left_click": 0,
        "right_click": 0,
        "scroll": 0,
        "move": 0
    }
    screenshot_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip per effetto specchio
            frame = cv2.flip(frame, 1)
            
            # Processa controllo mouse
            start_time = time.time()
            hand_data = controller.process_frame(frame)
            inference_time = (time.time() - start_time) * 1000
            
            # Conta azioni
            if hand_data and hand_data['action'] in action_counts:
                action_counts[hand_data['action']] += 1
            
            # Disegna controllo mouse
            frame = controller.draw_mouse_control(frame, hand_data)
            
            # Calcola FPS
            fps_counter += 1
            if time.time() - fps_start_time >= 1.0:
                current_fps = fps_counter
                fps_counter = 0
                fps_start_time = time.time()
            
            # Informazioni
            total_actions = sum(action_counts.values())
            info_text = [
                f"FPS: {current_fps}",
                f"Inference: {inference_time:.1f}ms",
                f"Mouse: {'ATTIVO' if controller.mouse_enabled else 'DISATTIVO'}",
                f"Movimento: {controller.movement_mode.upper()} | Sens: {controller.sensitivity:.1f}x",
                f"Mirror X: {'SI' if controller.mirror_x else 'NO'}",
                f"Left: {action_counts['left_click']} | Right: {action_counts['right_click']}",
                f"Scroll: {action_counts['scroll']} | Move: {action_counts['move']}"
            ]
            
            # Disegna info
            y_offset = 30
            for i, text in enumerate(info_text):
                color = (0, 255, 0) if i != 2 else ((0, 255, 0) if controller.mouse_enabled else (0, 0, 255))
                cv2.putText(frame, text, (10, y_offset + i * 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # Istruzioni
            cv2.putText(frame, "m:mouse x:mirror +:sens+ -:sens- v:speed q:quit s:save",
                       (10, frame.shape[0] - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(frame, f"Zona controllo: {controller.control_width}x{controller.control_height}",
                       (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)
            
            # Warning se mouse attivo
            if controller.mouse_enabled:
                cv2.putText(frame, "MOUSE VIRTUALE ATTIVO!", 
                           (frame.shape[1]//2 - 150, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            # Mostra frame
            cv2.imshow('Mouse Control Test', frame)
            
            # Input
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q') or key == 27:
                break
            elif key == ord('m'):
                controller.toggle_mouse()
            elif key == ord('x'):
                controller.toggle_mirror_x()
            elif key == ord('+') or key == ord('='):
                controller.change_sensitivity(0.1)
            elif key == ord('-'):
                controller.change_sensitivity(-0.1)
            elif key == ord('v'):
                controller.cycle_movement_mode()
            elif key == ord('s'):
                screenshot_count += 1
                filename = f"mouse_test_{screenshot_count}.jpg"
                cv2.imwrite(filename, frame)
                print(f"📸 Screenshot salvato: {filename}")
    
    except KeyboardInterrupt:
        print("\n⏹️ Interruzione da tastiera")
    except pyautogui.FailSafeException:
        print("\n🛑 FAILSAFE ATTIVATO - Sistema fermato per sicurezza")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n📊 STATISTICHE MOUSE AVANZATE:")
        print(f"   🖱️ Left Click: {action_counts['left_click']}")
        print(f"   🖱️ Right Click: {action_counts['right_click']}")
        print(f"   📜 Scroll: {action_counts['scroll']}")
        print(f"   👆 Move: {action_counts['move']}")
        print(f"   📸 Screenshot: {screenshot_count}")
        total_actions = sum(action_counts.values())
        print(f"   🎯 Azioni totali: {total_actions}")
        print("✅ Test mouse avanzato completato")

if __name__ == "__main__":
    main()
