#!/usr/bin/env python3
"""
Test Controllo Mouse Virtuale con Gesti
ATTENZIONE: Questo script controlla il mouse del sistema!
"""

import cv2
import mediapipe as mp
import pyautogui
import math
import time
from collections import deque

class MouseController:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,  # Solo una mano per controllo mouse
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        # Configurazioni mouse
        self.screen_width, self.screen_height = pyautogui.size()
        self.mouse_smoothing = deque(maxlen=5)
        self.click_threshold = 40
        self.last_click_time = 0
        self.mouse_enabled = False  # Disabilitato di default
        
        # Sicurezza
        pyautogui.FAILSAFE = True  # Muovi mouse in angolo per fermare
        
        print("✅ Mouse Controller inizializzato")
        print(f"🖥️ Risoluzione schermo: {self.screen_width}x{self.screen_height}")
        print("⚠️ SICUREZZA: Muovi mouse in angolo superiore sinistro per fermare")
    
    def calculate_distance(self, point1, point2):
        """Calcola distanza tra due punti"""
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def detect_pinch(self, landmarks):
        """Rileva gesto pinch per click"""
        thumb_tip = landmarks.landmark[4]
        index_tip = landmarks.landmark[8]
        
        # Converti in coordinate pixel
        h, w = 480, 640
        thumb_px = (int(thumb_tip.x * w), int(thumb_tip.y * h))
        index_px = (int(index_tip.x * w), int(index_tip.y * h))
        
        distance = self.calculate_distance(thumb_px, index_px)
        return distance < self.click_threshold
    
    def control_mouse(self, landmarks):
        """Controlla mouse con posizione indice"""
        if not self.mouse_enabled:
            return False, 0
        
        # Usa punta dell'indice per posizione mouse
        index_tip = landmarks.landmark[8]
        
        # Converti coordinate normalizzate in coordinate schermo
        # Inverti X per effetto specchio
        screen_x = int((1 - index_tip.x) * self.screen_width)
        screen_y = int(index_tip.y * self.screen_height)
        
        # Smoothing del movimento
        self.mouse_smoothing.append((screen_x, screen_y))
        
        if len(self.mouse_smoothing) > 0:
            # Media mobile per movimento fluido
            avg_x = sum(pos[0] for pos in self.mouse_smoothing) // len(self.mouse_smoothing)
            avg_y = sum(pos[1] for pos in self.mouse_smoothing) // len(self.mouse_smoothing)
            
            try:
                # Muovi mouse
                pyautogui.moveTo(avg_x, avg_y, duration=0.1)
                
                # Rileva click con pinch
                is_pinch = self.detect_pinch(landmarks)
                pinch_distance = self.calculate_distance(
                    (int(landmarks.landmark[4].x * 640), int(landmarks.landmark[4].y * 480)),
                    (int(landmarks.landmark[8].x * 640), int(landmarks.landmark[8].y * 480))
                )
                
                if is_pinch:
                    current_time = time.time()
                    if current_time - self.last_click_time > 1.0:  # Evita click multipli
                        pyautogui.click()
                        self.last_click_time = current_time
                        print("🖱️ CLICK!")
                        return True, pinch_distance
                
                return False, pinch_distance
                
            except pyautogui.FailSafeException:
                print("🛑 FAILSAFE ATTIVATO - Mouse fermato!")
                self.mouse_enabled = False
                return False, 0
        
        return False, 0
    
    def toggle_mouse(self):
        """Attiva/disattiva controllo mouse"""
        self.mouse_enabled = not self.mouse_enabled
        status = "ATTIVO" if self.mouse_enabled else "DISATTIVO"
        print(f"🖱️ Controllo mouse: {status}")
        
        if self.mouse_enabled:
            print("⚠️ ATTENZIONE: Mouse virtuale attivo!")
            print("   - Usa INDICE per muovere cursore")
            print("   - Usa PINCH (pollice+indice) per click")
            print("   - Muovi mouse in angolo per FERMARE")
    
    def process_frame(self, frame):
        """Processa frame per controllo mouse"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_frame)
        
        hand_data = None
        clicked = False
        pinch_distance = 0
        
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Usa solo la prima mano rilevata
                clicked, pinch_distance = self.control_mouse(hand_landmarks)
                
                hand_data = {
                    'landmarks': hand_landmarks,
                    'clicked': clicked,
                    'pinch_distance': pinch_distance
                }
                break  # Solo una mano
        
        return hand_data
    
    def draw_mouse_control(self, frame, hand_data):
        """Disegna visualizzazione controllo mouse"""
        if hand_data:
            landmarks = hand_data['landmarks']
            clicked = hand_data['clicked']
            pinch_distance = hand_data['pinch_distance']
            
            # Disegna landmarks
            self.mp_drawing.draw_landmarks(
                frame, landmarks, self.mp_hands.HAND_CONNECTIONS,
                self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                self.mp_drawing.DrawingSpec(color=(255, 255, 255), thickness=2)
            )
            
            # Evidenzia indice (controllo mouse)
            index_tip = landmarks.landmark[8]
            h, w, _ = frame.shape
            index_x, index_y = int(index_tip.x * w), int(index_tip.y * h)
            
            # Cerchio su indice
            color = (0, 0, 255) if clicked else (255, 0, 0)
            cv2.circle(frame, (index_x, index_y), 10, color, -1)
            
            # Evidenzia pollice (per pinch)
            thumb_tip = landmarks.landmark[4]
            thumb_x, thumb_y = int(thumb_tip.x * w), int(thumb_tip.y * h)
            cv2.circle(frame, (thumb_x, thumb_y), 8, (0, 255, 255), -1)
            
            # Linea tra pollice e indice
            cv2.line(frame, (thumb_x, thumb_y), (index_x, index_y), (255, 255, 0), 2)
            
            # Testo distanza pinch
            cv2.putText(frame, f"Pinch: {pinch_distance:.1f}", (10, 120), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # Stato click
            if clicked:
                cv2.putText(frame, "CLICK!", (10, 160), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 3)
        
        return frame

def main():
    print("🖱️ TEST CONTROLLO MOUSE VIRTUALE")
    print("=" * 50)
    print("⚠️ ATTENZIONE: Questo script controlla il mouse!")
    print("🛑 SICUREZZA: Muovi mouse in angolo superiore sinistro per fermare")
    print("=" * 50)
    
    # Inizializza controller
    controller = MouseController()
    
    # Inizializza webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Impossibile aprire la webcam")
        return
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print("✅ Webcam inizializzata")
    print("\n📋 COMANDI:")
    print("   - 'm': Attiva/disattiva controllo mouse")
    print("   - 'q': Esci")
    print("   - 's': Screenshot")
    print("\n🎯 CONTROLLI MOUSE:")
    print("   - INDICE: Muove cursore")
    print("   - PINCH: Click (pollice + indice vicini)")
    
    # Statistiche
    fps_counter = 0
    fps_start_time = time.time()
    current_fps = 0
    total_clicks = 0
    screenshot_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip per effetto specchio
            frame = cv2.flip(frame, 1)
            
            # Processa controllo mouse
            start_time = time.time()
            hand_data = controller.process_frame(frame)
            inference_time = (time.time() - start_time) * 1000
            
            # Conta click
            if hand_data and hand_data['clicked']:
                total_clicks += 1
            
            # Disegna controllo mouse
            frame = controller.draw_mouse_control(frame, hand_data)
            
            # Calcola FPS
            fps_counter += 1
            if time.time() - fps_start_time >= 1.0:
                current_fps = fps_counter
                fps_counter = 0
                fps_start_time = time.time()
            
            # Informazioni
            info_text = [
                f"FPS: {current_fps}",
                f"Inference: {inference_time:.1f}ms",
                f"Mouse: {'ATTIVO' if controller.mouse_enabled else 'DISATTIVO'}",
                f"Click totali: {total_clicks}",
                f"Schermo: {controller.screen_width}x{controller.screen_height}"
            ]
            
            # Disegna info
            y_offset = 30
            for i, text in enumerate(info_text):
                color = (0, 255, 0) if i != 2 else ((0, 255, 0) if controller.mouse_enabled else (0, 0, 255))
                cv2.putText(frame, text, (10, y_offset + i * 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            # Istruzioni
            cv2.putText(frame, "m:toggle mouse  q:quit  s:save", 
                       (10, frame.shape[0] - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Warning se mouse attivo
            if controller.mouse_enabled:
                cv2.putText(frame, "MOUSE VIRTUALE ATTIVO!", 
                           (frame.shape[1]//2 - 150, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            # Mostra frame
            cv2.imshow('Mouse Control Test', frame)
            
            # Input
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q') or key == 27:
                break
            elif key == ord('m'):
                controller.toggle_mouse()
            elif key == ord('s'):
                screenshot_count += 1
                filename = f"mouse_test_{screenshot_count}.jpg"
                cv2.imwrite(filename, frame)
                print(f"📸 Screenshot salvato: {filename}")
    
    except KeyboardInterrupt:
        print("\n⏹️ Interruzione da tastiera")
    except pyautogui.FailSafeException:
        print("\n🛑 FAILSAFE ATTIVATO - Sistema fermato per sicurezza")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n📊 STATISTICHE MOUSE:")
        print(f"   🖱️ Click totali: {total_clicks}")
        print(f"   📸 Screenshot: {screenshot_count}")
        print("✅ Test mouse completato")

if __name__ == "__main__":
    main()
