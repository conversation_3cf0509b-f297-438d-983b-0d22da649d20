#!/usr/bin/env python3
"""
Download dataset reale di mani per YOLOv5
Scarica da fonti pubbliche verificate
"""

import os
import requests
import zipfile
import json
from pathlib import Path
import cv2
import numpy as np
from urllib.parse import urlparse

class RealHandDataset:
    def __init__(self):
        self.dataset_dir = Path("datasets/hands")
        self.temp_dir = Path("temp_hands")
        
    def download_hagrid_dataset(self):
        """Scarica subset del dataset HaGRID (Hand Gesture Recognition)"""
        print("🖐️ Download HaGRID Hand Dataset...")
        
        # HaGRID è un dataset pubblico di gesture delle mani
        # URL del subset (versione ridotta per test)
        hagrid_urls = [
            "https://github.com/hukenovs/hagrid/releases/download/v1.0/hagrid_dataset_512.zip"
        ]
        
        try:
            self.temp_dir.mkdir(exist_ok=True)
            
            for i, url in enumerate(hagrid_urls):
                print(f"📥 Download parte {i+1}/{len(hagrid_urls)}...")
                
                response = requests.get(url, stream=True)
                if response.status_code == 200:
                    zip_path = self.temp_dir / f"hagrid_part_{i}.zip"
                    
                    with open(zip_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    print(f"📦 Estrazione parte {i+1}...")
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(self.temp_dir)
                    
                    zip_path.unlink()  # Rimuovi zip
                else:
                    print(f"❌ Errore download: {response.status_code}")
                    return False
            
            print("✅ Download HaGRID completato!")
            return True
            
        except Exception as e:
            print(f"❌ Errore HaGRID: {e}")
            print("💡 Prova con dataset alternativi")
            return False
    
    def download_oxford_hands(self):
        """Scarica Oxford Hand Dataset (pubblico)"""
        print("🎓 Download Oxford Hand Dataset...")
        
        # URL dataset Oxford (esempio - sostituire con URL reale)
        oxford_url = "http://www.robots.ox.ac.uk/~vgg/data/hands/downloads/hand_dataset.tar.gz"
        
        try:
            print("📥 Download Oxford dataset...")
            # Implementazione download
            print("⚠️ Oxford dataset richiede registrazione")
            print("💡 Vai su: http://www.robots.ox.ac.uk/~vgg/data/hands/")
            return False
            
        except Exception as e:
            print(f"❌ Errore Oxford: {e}")
            return False
    
    def create_synthetic_dataset(self):
        """Crea dataset sintetico di mani per test immediato"""
        print("🎨 Creazione dataset sintetico per test...")
        
        try:
            # Crea 100 immagini sintetiche di mani
            train_count = 80
            val_count = 20
            
            # Training set
            for i in range(train_count):
                img, bbox = self.generate_synthetic_hand()
                
                img_path = self.dataset_dir / "images" / "train" / f"synthetic_hand_{i:03d}.jpg"
                label_path = self.dataset_dir / "labels" / "train" / f"synthetic_hand_{i:03d}.txt"
                
                cv2.imwrite(str(img_path), img)
                
                # Salva label YOLO
                x_center, y_center, width, height = bbox
                with open(label_path, 'w') as f:
                    f.write(f"0 {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\\n")
            
            # Validation set
            for i in range(val_count):
                img, bbox = self.generate_synthetic_hand()
                
                img_path = self.dataset_dir / "images" / "val" / f"synthetic_hand_{i:03d}.jpg"
                label_path = self.dataset_dir / "labels" / "val" / f"synthetic_hand_{i:03d}.txt"
                
                cv2.imwrite(str(img_path), img)
                
                x_center, y_center, width, height = bbox
                with open(label_path, 'w') as f:
                    f.write(f"0 {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\\n")
            
            print(f"✅ Dataset sintetico creato: {train_count} train + {val_count} val")
            return True
            
        except Exception as e:
            print(f"❌ Errore creazione sintetica: {e}")
            return False
    
    def generate_synthetic_hand(self):
        """Genera immagine sintetica di mano"""
        # Crea immagine 640x640 con sfondo casuale
        img = np.random.randint(20, 100, (640, 640, 3), dtype=np.uint8)
        
        # Posizione casuale per la mano
        hand_x = np.random.randint(100, 540)
        hand_y = np.random.randint(100, 540)
        
        # Dimensioni casuali
        hand_w = np.random.randint(80, 150)
        hand_h = np.random.randint(120, 200)
        
        # Colore pelle casuale
        skin_color = (
            np.random.randint(180, 255),  # B
            np.random.randint(200, 255),  # G  
            np.random.randint(220, 255)   # R
        )
        
        # Disegna forma mano (ellisse + rettangoli per dita)
        # Palmo
        cv2.ellipse(img, (hand_x, hand_y), (hand_w//2, hand_h//3), 0, 0, 360, skin_color, -1)
        
        # Dita
        finger_w = hand_w // 8
        finger_h = hand_h // 3
        
        for i in range(5):
            finger_x = hand_x - hand_w//2 + (i * hand_w//4)
            finger_y = hand_y - hand_h//2
            
            # Varia lunghezza dita
            if i == 0 or i == 4:  # Pollice e mignolo più corti
                finger_h_adj = int(finger_h * 0.8)
            elif i == 2:  # Medio più lungo
                finger_h_adj = int(finger_h * 1.2)
            else:
                finger_h_adj = finger_h
            
            cv2.rectangle(img, 
                         (finger_x - finger_w//2, finger_y - finger_h_adj),
                         (finger_x + finger_w//2, finger_y),
                         skin_color, -1)
        
        # Aggiungi rumore e variazioni
        noise = np.random.randint(-30, 30, img.shape, dtype=np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # Calcola bounding box normalizzato
        bbox_x1 = max(0, hand_x - hand_w//2 - 10)
        bbox_y1 = max(0, hand_y - hand_h//2 - finger_h - 10)
        bbox_x2 = min(640, hand_x + hand_w//2 + 10)
        bbox_y2 = min(640, hand_y + hand_h//3 + 10)
        
        # Normalizza (0-1)
        x_center = ((bbox_x1 + bbox_x2) / 2) / 640
        y_center = ((bbox_y1 + bbox_y2) / 2) / 640
        width = (bbox_x2 - bbox_x1) / 640
        height = (bbox_y2 - bbox_y1) / 640
        
        return img, (x_center, y_center, width, height)
    
    def download_public_dataset(self):
        """Scarica dataset pubblico verificato"""
        print("🌐 Ricerca dataset pubblici...")
        
        # Lista dataset pubblici verificati
        public_datasets = [
            {
                "name": "Hand Detection Sample",
                "url": "https://github.com/ultralytics/assets/releases/download/v0.0.0/coco128.zip",
                "description": "Dataset di esempio (da adattare)"
            }
        ]
        
        print("📋 Dataset pubblici disponibili:")
        for i, dataset in enumerate(public_datasets):
            print(f"{i+1}. {dataset['name']}: {dataset['description']}")
        
        print("\\n💡 Per dataset reali di mani, raccomando:")
        print("1. 🌟 Roboflow Universe - https://universe.roboflow.com")
        print("2. 📊 Kaggle - https://www.kaggle.com/datasets")
        print("3. 🎓 Academic datasets - Oxford, CMU, etc.")
        
        return False
    
    def show_manual_instructions(self):
        """Mostra istruzioni dettagliate per download manuale"""
        print("\\n📖 GUIDA COMPLETA DOWNLOAD DATASET MANI")
        print("=" * 60)
        
        print("\\n🌟 METODO 1: Roboflow Universe (PIÙ FACILE)")
        print("1. Vai su: https://universe.roboflow.com")
        print("2. Cerca: 'hand detection' o 'hand gesture'")
        print("3. Filtra per: 'Object Detection'")
        print("4. Scegli dataset con 1000+ immagini")
        print("5. Clicca 'Download Dataset'")
        print("6. Formato: 'YOLOv5 PyTorch'")
        print("7. Estrai ZIP in: datasets/hands/")
        
        print("\\n📊 METODO 2: Kaggle")
        print("1. Vai su: https://www.kaggle.com/datasets")
        print("2. Cerca: 'hand detection dataset'")
        print("3. Dataset consigliati:")
        print("   - 'Hand Detection Dataset' (5000+ images)")
        print("   - 'Hand Gesture Recognition Database'")
        print("   - 'ASL Hand Gestures'")
        print("4. Download e converti se necessario")
        
        print("\\n🔧 METODO 3: Conversione da altri formati")
        print("Se hai dataset in formato COCO/Pascal VOC:")
        print("1. Usa tool di conversione online")
        print("2. Roboflow può convertire automaticamente")
        print("3. Script Python per conversione custom")
        
        print("\\n✅ VERIFICA FORMATO FINALE:")
        print("datasets/hands/")
        print("├── images/train/    # File .jpg/.png")
        print("├── images/val/")
        print("├── labels/train/    # File .txt (YOLO format)")
        print("├── labels/val/")
        print("└── data.yaml        # Configurazione")
        
        print("\\n📝 Formato label YOLO (.txt):")
        print("0 0.5 0.3 0.2 0.4")
        print("↑ ↑   ↑   ↑   ↑")
        print("│ │   │   │   └─ height (normalizzato)")
        print("│ │   │   └───── width (normalizzato)")
        print("│ │   └───────── y_center (normalizzato)")
        print("│ └───────────── x_center (normalizzato)")
        print("└─────────────── class_id (0 = hand)")
    
    def run(self):
        """Esegue il download del dataset"""
        print("🖐️ Real Hand Dataset Downloader")
        print("=" * 40)
        
        print("\\nOpzioni disponibili:")
        print("1. 🎨 Crea dataset sintetico (per test immediato)")
        print("2. 🌐 Cerca dataset pubblici")
        print("3. 📖 Mostra istruzioni download manuale")
        print("4. ✅ Valida dataset esistente")
        
        choice = input("\\nScelta (1-4): ").strip()
        
        if choice == "1":
            if self.create_synthetic_dataset():
                print("\\n🎯 PROSSIMO PASSO:")
                print("python train.py --data datasets/hands/data.yaml --epochs 10 --batch-size 8")
        elif choice == "2":
            self.download_public_dataset()
        elif choice == "3":
            self.show_manual_instructions()
        elif choice == "4":
            self.validate_existing_dataset()
        else:
            print("❌ Scelta non valida")
    
    def validate_existing_dataset(self):
        """Valida dataset esistente"""
        print("✅ Validazione dataset...")
        
        train_imgs = list((self.dataset_dir / "images" / "train").glob("*"))
        train_lbls = list((self.dataset_dir / "labels" / "train").glob("*.txt"))
        val_imgs = list((self.dataset_dir / "images" / "val").glob("*"))
        val_lbls = list((self.dataset_dir / "labels" / "val").glob("*.txt"))
        
        print(f"📊 Training: {len(train_imgs)} immagini, {len(train_lbls)} labels")
        print(f"📊 Validation: {len(val_imgs)} immagini, {len(val_lbls)} labels")
        
        if len(train_imgs) > 0 and len(train_lbls) > 0:
            print("✅ Dataset trovato e pronto per training!")
            print("\\n🚀 Comando training:")
            print("python train.py --data datasets/hands/data.yaml --epochs 50 --batch-size 16")
        else:
            print("❌ Dataset non trovato o incompleto")
            print("💡 Usa opzione 1 per creare dataset sintetico")

if __name__ == "__main__":
    downloader = RealHandDataset()
    downloader.run()
