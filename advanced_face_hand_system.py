#!/usr/bin/env python3
"""
Sistema Avanzato Face + Hand Detection con TOP 3 Funzionalità:
1. Riconoscimento Gesti delle Mani
2. Controllo Mouse Virtuale  
3. Riconoscimento Emozioni Facciali
"""

import cv2
import numpy as np
import time
import mediapipe as mp
import pyautogui
import math
from collections import deque

class AdvancedDetectionSystem:
    def __init__(self):
        # Inizializza detector base
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # MediaPipe per mani e viso
        self.mp_hands = mp.solutions.hands
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_drawing = mp.solutions.drawing_utils
        
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=2,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Configurazioni sistema
        self.gesture_enabled = True
        self.mouse_enabled = False  # Disabilitato di default per sicurezza
        self.emotion_enabled = True
        
        # Mouse virtuale
        self.screen_width, self.screen_height = pyautogui.size()
        self.mouse_smoothing = deque(maxlen=5)
        self.click_threshold = 30  # Distanza per click
        self.last_click_time = 0
        
        # Gesture recognition
        self.gesture_history = deque(maxlen=10)
        self.current_gesture = "None"
        
        # Emotion recognition (semplificato)
        self.current_emotion = "Neutral"
        self.emotion_confidence = 0.0
        
        print("✅ Sistema Avanzato inizializzato")
        print("🤏 Gesti: ATTIVO")
        print("🖱️ Mouse: DISATTIVO (premi 'm' per attivare)")
        print("😊 Emozioni: ATTIVO")
    
    def calculate_distance(self, point1, point2):
        """Calcola distanza euclidea tra due punti"""
        return math.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    def recognize_hand_gesture(self, landmarks, hand_label):
        """Riconosce gesti della mano"""
        if not self.gesture_enabled:
            return "Disabled"
        
        # Estrai coordinate landmarks chiave
        thumb_tip = landmarks.landmark[4]
        thumb_ip = landmarks.landmark[3]
        index_tip = landmarks.landmark[8]
        index_pip = landmarks.landmark[6]
        middle_tip = landmarks.landmark[12]
        middle_pip = landmarks.landmark[10]
        ring_tip = landmarks.landmark[16]
        ring_pip = landmarks.landmark[14]
        pinky_tip = landmarks.landmark[20]
        pinky_pip = landmarks.landmark[18]
        
        # Converti in coordinate pixel (assumendo frame 640x480)
        h, w = 480, 640
        thumb_tip_px = (int(thumb_tip.x * w), int(thumb_tip.y * h))
        index_tip_px = (int(index_tip.x * w), int(index_tip.y * h))
        middle_tip_px = (int(middle_tip.x * w), int(middle_tip.y * h))
        
        # Rileva dita estese
        fingers_up = []
        
        # Pollice (diverso per mano destra/sinistra)
        if hand_label == "Right":
            fingers_up.append(thumb_tip.x > thumb_ip.x)
        else:
            fingers_up.append(thumb_tip.x < thumb_ip.x)
        
        # Altre dita
        fingers_up.append(index_tip.y < index_pip.y)
        fingers_up.append(middle_tip.y < middle_pip.y)
        fingers_up.append(ring_tip.y < ring_pip.y)
        fingers_up.append(pinky_tip.y < pinky_pip.y)
        
        total_fingers = sum(fingers_up)
        
        # Riconoscimento gesti
        gesture = "Unknown"
        
        if total_fingers == 0:
            gesture = "Fist"
        elif total_fingers == 1 and fingers_up[1]:
            gesture = "Point"
        elif total_fingers == 2 and fingers_up[1] and fingers_up[2]:
            gesture = "Peace"
        elif total_fingers == 3 and fingers_up[1] and fingers_up[2] and fingers_up[3]:
            gesture = "Three"
        elif total_fingers == 4 and not fingers_up[0]:
            gesture = "Four"
        elif total_fingers == 5:
            gesture = "Open_Hand"
        elif fingers_up[0] and fingers_up[1] and not any(fingers_up[2:]):
            gesture = "Gun"
        elif fingers_up[0] and not any(fingers_up[1:]):
            gesture = "Thumbs_Up"
        
        # Gesti speciali basati su distanze
        thumb_index_dist = self.calculate_distance(thumb_tip_px, index_tip_px)
        if thumb_index_dist < 30:
            gesture = "Pinch"
        
        return gesture
    
    def control_mouse(self, landmarks, gesture):
        """Controlla mouse con gesti mano"""
        if not self.mouse_enabled:
            return
        
        # Usa punta dell'indice per controllo mouse
        index_tip = landmarks.landmark[8]
        thumb_tip = landmarks.landmark[4]
        
        # Converti coordinate normalizzate in coordinate schermo
        screen_x = int(index_tip.x * self.screen_width)
        screen_y = int(index_tip.y * self.screen_height)
        
        # Smoothing del movimento
        self.mouse_smoothing.append((screen_x, screen_y))
        if len(self.mouse_smoothing) > 0:
            avg_x = sum(pos[0] for pos in self.mouse_smoothing) // len(self.mouse_smoothing)
            avg_y = sum(pos[1] for pos in self.mouse_smoothing) // len(self.mouse_smoothing)
            
            # Muovi mouse
            pyautogui.moveTo(avg_x, avg_y, duration=0.1)
            
            # Click con gesto pinch
            if gesture == "Pinch":
                current_time = time.time()
                if current_time - self.last_click_time > 1.0:  # Evita click multipli
                    pyautogui.click()
                    self.last_click_time = current_time
                    print("🖱️ Click!")
    
    def analyze_facial_emotion(self, face_landmarks):
        """Analizza emozioni facciali (versione semplificata)"""
        if not self.emotion_enabled:
            return "Disabled", 0.0
        
        # Punti chiave per analisi emozioni
        # Questo è un'implementazione semplificata
        # Per risultati migliori si userebbe un modello ML dedicato
        
        try:
            # Estrai alcuni punti chiave
            landmarks = face_landmarks.landmark
            
            # Punti bocca (semplificato)
            mouth_left = landmarks[61]
            mouth_right = landmarks[291]
            mouth_top = landmarks[13]
            mouth_bottom = landmarks[14]
            
            # Punti occhi
            left_eye_top = landmarks[159]
            left_eye_bottom = landmarks[145]
            right_eye_top = landmarks[386]
            right_eye_bottom = landmarks[374]
            
            # Calcola "sorriso" basato sulla larghezza della bocca
            mouth_width = abs(mouth_right.x - mouth_left.x)
            mouth_height = abs(mouth_top.y - mouth_bottom.y)
            
            # Calcola apertura occhi
            left_eye_ratio = abs(left_eye_top.y - left_eye_bottom.y)
            right_eye_ratio = abs(right_eye_top.y - right_eye_bottom.y)
            avg_eye_ratio = (left_eye_ratio + right_eye_ratio) / 2
            
            # Logica semplificata per emozioni
            if mouth_width > 0.05 and mouth_height < 0.02:
                emotion = "Happy"
                confidence = min(mouth_width * 10, 1.0)
            elif avg_eye_ratio < 0.01:
                emotion = "Sleepy"
                confidence = 0.7
            elif mouth_height > 0.03:
                emotion = "Surprised"
                confidence = min(mouth_height * 15, 1.0)
            else:
                emotion = "Neutral"
                confidence = 0.5
            
            return emotion, confidence
            
        except Exception as e:
            return "Error", 0.0
    
    def detect_faces(self, frame):
        """Rileva visi con Haar Cascades"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 5, minSize=(50, 50))
        return [(x, y, w, h, 'face') for x, y, w, h in faces]
    
    def detect_hands_and_gestures(self, frame):
        """Rileva mani e riconosce gesti"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_frame)
        
        hand_detections = []
        
        if results.multi_hand_landmarks:
            for hand_idx, hand_landmarks in enumerate(results.multi_hand_landmarks):
                # Calcola bounding box
                h, w, _ = frame.shape
                x_coords = [landmark.x * w for landmark in hand_landmarks.landmark]
                y_coords = [landmark.y * h for landmark in hand_landmarks.landmark]
                
                x_min, x_max = int(min(x_coords)), int(max(x_coords))
                y_min, y_max = int(min(y_coords)), int(max(y_coords))
                
                # Margine
                margin = 20
                x_min = max(0, x_min - margin)
                y_min = max(0, y_min - margin)
                x_max = min(w, x_max + margin)
                y_max = min(h, y_max + margin)
                
                # Info mano
                hand_label = results.multi_handedness[hand_idx].classification[0].label
                confidence = results.multi_handedness[hand_idx].classification[0].score
                
                # Riconosci gesto
                gesture = self.recognize_hand_gesture(hand_landmarks, hand_label)
                
                # Controlla mouse se abilitato
                if hand_label == "Right":  # Usa solo mano destra per mouse
                    self.control_mouse(hand_landmarks, gesture)
                
                hand_detections.append({
                    'bbox': (x_min, y_min, x_max - x_min, y_max - y_min),
                    'landmarks': hand_landmarks,
                    'label': hand_label,
                    'confidence': confidence,
                    'gesture': gesture
                })
        
        return hand_detections
    
    def detect_facial_emotions(self, frame):
        """Rileva emozioni facciali"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(rgb_frame)
        
        if results.multi_face_landmarks:
            for face_landmarks in results.multi_face_landmarks:
                emotion, confidence = self.analyze_facial_emotion(face_landmarks)
                self.current_emotion = emotion
                self.emotion_confidence = confidence
                return face_landmarks
        
        return None
    
    def draw_detections(self, frame, faces, hands, face_mesh_landmarks):
        """Disegna tutte le detection"""
        # Disegna visi
        for face in faces:
            x, y, w, h, _ = face
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # Emozione
            emotion_text = f"{self.current_emotion} ({self.emotion_confidence:.2f})"
            cv2.putText(frame, emotion_text, (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        # Disegna mesh facciale (semplificato)
        if face_mesh_landmarks and self.emotion_enabled:
            self.mp_drawing.draw_landmarks(
                frame, face_mesh_landmarks, self.mp_face_mesh.FACEMESH_CONTOURS,
                None, self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=1, circle_radius=1)
            )
        
        # Disegna mani e gesti
        for hand in hands:
            bbox = hand['bbox']
            landmarks = hand['landmarks']
            label = hand['label']
            gesture = hand['gesture']
            confidence = hand['confidence']
            
            x, y, w, h = bbox
            
            # Colore basato sulla mano
            color = (255, 0, 0) if label == "Right" else (0, 0, 255)
            
            # Bounding box
            cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
            
            # Gesto
            gesture_text = f"{label}: {gesture}"
            cv2.putText(frame, gesture_text, (x, y - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            # Confidence
            cv2.putText(frame, f"{confidence:.2f}", (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # Landmarks mano
            self.mp_drawing.draw_landmarks(
                frame, landmarks, self.mp_hands.HAND_CONNECTIONS,
                self.mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
                self.mp_drawing.DrawingSpec(color=(255, 255, 255), thickness=2)
            )
        
        return frame
    
    def toggle_gesture_detection(self):
        """Toggle riconoscimento gesti"""
        self.gesture_enabled = not self.gesture_enabled
        status = "ATTIVO" if self.gesture_enabled else "DISATTIVO"
        print(f"🤏 Riconoscimento gesti: {status}")
    
    def toggle_mouse_control(self):
        """Toggle controllo mouse"""
        self.mouse_enabled = not self.mouse_enabled
        status = "ATTIVO" if self.mouse_enabled else "DISATTIVO"
        print(f"🖱️ Controllo mouse: {status}")
        if self.mouse_enabled:
            print("⚠️ ATTENZIONE: Mouse virtuale attivo! Usa gesto 'Pinch' per click")
    
    def toggle_emotion_detection(self):
        """Toggle riconoscimento emozioni"""
        self.emotion_enabled = not self.emotion_enabled
        status = "ATTIVO" if self.emotion_enabled else "DISATTIVO"
        print(f"😊 Riconoscimento emozioni: {status}")

def main():
    """Funzione principale"""
    print("🚀 SISTEMA AVANZATO FACE + HAND DETECTION")
    print("=" * 60)
    print("🎯 TOP 3 FUNZIONALITÀ:")
    print("   1. 🤏 Riconoscimento Gesti")
    print("   2. 🖱️ Controllo Mouse Virtuale")
    print("   3. 😊 Riconoscimento Emozioni")
    print("=" * 60)
    
    # Inizializza sistema
    try:
        detector = AdvancedDetectionSystem()
    except Exception as e:
        print(f"❌ Errore inizializzazione: {e}")
        return
    
    # Inizializza webcam
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Impossibile aprire la webcam")
        return
    
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
    
    print("\n📋 COMANDI:")
    print("   - 'q' o ESC: Esci")
    print("   - 's': Salva screenshot")
    print("   - 'g': Toggle gesti")
    print("   - 'm': Toggle mouse virtuale")
    print("   - 'e': Toggle emozioni")
    print("   - 'r': Reset contatori")
    
    # Variabili statistiche
    fps_counter = 0
    fps_start_time = time.time()
    current_fps = 0
    total_faces = 0
    total_hands = 0
    total_gestures = 0
    screenshot_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame = cv2.flip(frame, 1)
            
            # Detection
            start_time = time.time()
            
            faces = detector.detect_faces(frame)
            hands = detector.detect_hands_and_gestures(frame)
            face_mesh = detector.detect_facial_emotions(frame)
            
            inference_time = (time.time() - start_time) * 1000
            
            # Disegna detection
            frame = detector.draw_detections(frame, faces, hands, face_mesh)
            
            # Aggiorna contatori
            total_faces += len(faces)
            total_hands += len(hands)
            total_gestures += len([h for h in hands if h['gesture'] != "Unknown"])
            
            # FPS
            fps_counter += 1
            if time.time() - fps_start_time >= 1.0:
                current_fps = fps_counter
                fps_counter = 0
                fps_start_time = time.time()
            
            # Informazioni
            info_text = [
                f"FPS: {current_fps}",
                f"Inference: {inference_time:.1f}ms",
                f"Visi: {len(faces)} | Emozione: {detector.current_emotion}",
                f"Mani: {len(hands)} | Gesti: {len([h for h in hands if h['gesture'] != 'Unknown'])}",
                f"G:{detector.gesture_enabled} M:{detector.mouse_enabled} E:{detector.emotion_enabled}"
            ]
            
            # Disegna info
            y_offset = 30
            for i, text in enumerate(info_text):
                cv2.putText(frame, text, (10, y_offset + i * 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Istruzioni
            cv2.putText(frame, "q:quit s:save g:gestures m:mouse e:emotions r:reset", 
                       (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            cv2.imshow('Advanced Face + Hand Detection System', frame)
            
            # Input
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q') or key == 27:
                break
            elif key == ord('s'):
                screenshot_count += 1
                filename = f"advanced_detection_{screenshot_count}.jpg"
                cv2.imwrite(filename, frame)
                print(f"📸 Screenshot salvato: {filename}")
            elif key == ord('g'):
                detector.toggle_gesture_detection()
            elif key == ord('m'):
                detector.toggle_mouse_control()
            elif key == ord('e'):
                detector.toggle_emotion_detection()
            elif key == ord('r'):
                total_faces = total_hands = total_gestures = 0
                print("🔄 Contatori resettati")
    
    except KeyboardInterrupt:
        print("\n⏹️ Interruzione da tastiera")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        print(f"\n📊 STATISTICHE FINALI:")
        print(f"   👤 Visi totali: {total_faces}")
        print(f"   👋 Mani totali: {total_hands}")
        print(f"   🤏 Gesti riconosciuti: {total_gestures}")
        print(f"   📸 Screenshot: {screenshot_count}")
        print("✅ Sistema avanzato chiuso")

if __name__ == "__main__":
    main()
